import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';

const Layout = ({ children }) => {
  const [activeSection, setActiveSection] = useState('');
  const location = useLocation();

  const navItems = [
    { name: 'About Me', path: '/', id: 'about' },
    { name: 'Projects', path: '/projects', id: 'projects' },
    { name: 'Services', path: '/services', id: 'services' },
  ];

  useEffect(() => {
    // Update active section based on current route
    const currentPath = location.pathname;
    if (currentPath === '/') setActiveSection('about');
    else if (currentPath === '/projects') setActiveSection('projects');
    else if (currentPath === '/services') setActiveSection('services');
    else if (currentPath === '/contact') setActiveSection('contact');
  }, [location]);

  return (
    <div className="min-h-screen bg-white">
      {/* Sticky Navigation */}
      <motion.nav 
        className="fixed top-0 left-0 z-50 h-screen w-20 md:w-24 bg-white border-r border-gray-200 flex flex-col justify-between py-8"
        initial={{ x: -100, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        {/* Navigation Links */}
        <div className="flex flex-col space-y-8">
          {navItems.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ x: -50, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Link
                to={item.path}
                className={`block transform -rotate-90 origin-center whitespace-nowrap text-sm font-medium transition-all duration-300 hover:text-gray-600 ${
                  activeSection === item.id ? 'text-black font-semibold' : 'text-gray-500'
                }`}
                style={{ transformOrigin: 'center center' }}
              >
                {item.name}
              </Link>
            </motion.div>
          ))}
        </div>

        {/* Contact CTA Button */}
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Link
            to="/contact"
            className="block transform -rotate-90 origin-center bg-black text-white px-4 py-2 text-sm font-medium hover:bg-gray-800 transition-all duration-300 whitespace-nowrap"
            style={{ transformOrigin: 'center center' }}
          >
            Contact Now
          </Link>
        </motion.div>
      </motion.nav>

      {/* Main Content */}
      <main className="ml-20 md:ml-24">
        {children}
      </main>
    </div>
  );
};

export default Layout;
