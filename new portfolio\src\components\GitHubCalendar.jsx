import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

const GitHubCalendar = () => {
  const [calendarData, setCalendarData] = useState([]);

  useEffect(() => {
    // Generate mock GitHub-style calendar data
    const generateCalendarData = () => {
      const data = [];
      const today = new Date();
      const startDate = new Date(today.getFullYear(), today.getMonth() - 11, 1);
      
      for (let i = 0; i < 365; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);
        
        // Generate random commit intensity (0-4)
        const intensity = Math.floor(Math.random() * 5);
        data.push({
          date: date.toISOString().split('T')[0],
          intensity,
          commits: intensity * Math.floor(Math.random() * 3) + intensity
        });
      }
      return data;
    };

    setCalendarData(generateCalendarData());
  }, []);

  const getIntensityColor = (intensity) => {
    const colors = [
      'bg-gray-800', // 0 commits
      'bg-gray-600', // 1-2 commits
      'bg-gray-500', // 3-5 commits
      'bg-gray-400', // 6-8 commits
      'bg-white'     // 9+ commits
    ];
    return colors[intensity] || colors[0];
  };

  const weeks = [];
  for (let i = 0; i < calendarData.length; i += 7) {
    weeks.push(calendarData.slice(i, i + 7));
  }

  return (
    <div className="bg-black p-6 rounded-lg border border-gray-800">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="space-y-4"
      >
        <h3 className="text-white text-lg font-medium mb-4">GitHub Activity</h3>
        
        {/* Calendar Grid */}
        <div className="grid grid-cols-53 gap-1 max-w-full overflow-x-auto">
          {weeks.map((week, weekIndex) => (
            <div key={weekIndex} className="grid grid-rows-7 gap-1">
              {week.map((day, dayIndex) => (
                <motion.div
                  key={`${weekIndex}-${dayIndex}`}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ 
                    duration: 0.2, 
                    delay: (weekIndex * 7 + dayIndex) * 0.001 
                  }}
                  className={`w-3 h-3 rounded-sm ${getIntensityColor(day?.intensity || 0)} hover:ring-2 hover:ring-white hover:ring-opacity-50 transition-all duration-200 cursor-pointer`}
                  title={day ? `${day.commits} commits on ${day.date}` : ''}
                />
              ))}
            </div>
          ))}
        </div>

        {/* Legend */}
        <div className="flex items-center justify-between text-xs text-gray-400 mt-4">
          <span>Less</span>
          <div className="flex gap-1">
            {[0, 1, 2, 3, 4].map((intensity) => (
              <div
                key={intensity}
                className={`w-3 h-3 rounded-sm ${getIntensityColor(intensity)}`}
              />
            ))}
          </div>
          <span>More</span>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 mt-6 text-center">
          <div>
            <div className="text-2xl font-bold text-white">
              {calendarData.reduce((sum, day) => sum + day.commits, 0)}
            </div>
            <div className="text-sm text-gray-400">Total Commits</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-white">
              {calendarData.filter(day => day.commits > 0).length}
            </div>
            <div className="text-sm text-gray-400">Active Days</div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default GitHubCalendar;
