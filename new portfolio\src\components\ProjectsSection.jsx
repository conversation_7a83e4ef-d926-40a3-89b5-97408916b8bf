import { motion } from 'framer-motion';
import { ExternalLink, Github, Smartphone, Globe, Brain } from 'lucide-react';

const ProjectsSection = () => {
  const projects = [
    {
      id: 1,
      title: "AI-Powered Task Manager",
      description: "A smart task management application that uses machine learning to prioritize tasks and predict completion times. Built with React, Node.js, and TensorFlow.js.",
      tags: ["React", "Node.js", "AI/ML", "MongoDB", "TensorFlow.js"],
      type: "Web App",
      icon: Brain,
      github: "https://github.com/arkitkarmokar",
      demo: "https://demo.example.com",
      featured: true
    },
    {
      id: 2,
      title: "E-Commerce Platform",
      description: "Full-stack e-commerce solution with real-time inventory management, payment integration, and admin dashboard. Scalable architecture with microservices.",
      tags: ["React", "Express", "PostgreSQL", "Stripe", "Docker"],
      type: "Web App",
      icon: Globe,
      github: "https://github.com/arkitkarmokar",
      demo: "https://demo.example.com",
      featured: true
    },
    {
      id: 3,
      title: "Fitness Tracking App",
      description: "Android application for tracking workouts, nutrition, and progress. Features offline sync, custom workout plans, and social sharing capabilities.",
      tags: ["Android", "Kotlin", "Room DB", "Firebase", "Material Design"],
      type: "Mobile App",
      icon: Smartphone,
      github: "https://github.com/arkitkarmokar",
      demo: "https://play.google.com/store",
      featured: false
    },
    {
      id: 4,
      title: "Real-time Chat Application",
      description: "Scalable chat application with real-time messaging, file sharing, and video calls. Built with modern web technologies and WebRTC.",
      tags: ["React", "Socket.io", "WebRTC", "Node.js", "Redis"],
      type: "Web App",
      icon: Globe,
      github: "https://github.com/arkitkarmokar",
      demo: "https://demo.example.com",
      featured: false
    },
    {
      id: 5,
      title: "Smart Home Dashboard",
      description: "IoT dashboard for controlling and monitoring smart home devices. Features real-time data visualization and automated scheduling.",
      tags: ["React", "Python", "IoT", "MQTT", "InfluxDB"],
      type: "IoT App",
      icon: Brain,
      github: "https://github.com/arkitkarmokar",
      demo: "https://demo.example.com",
      featured: false
    },
    {
      id: 6,
      title: "Portfolio Website",
      description: "Modern, responsive portfolio website built with React and Framer Motion. Features smooth animations and optimized performance.",
      tags: ["React", "Tailwind", "Framer Motion", "Vite"],
      type: "Web App",
      icon: Globe,
      github: "https://github.com/arkitkarmokar",
      demo: "https://arkitkarmokar.dev",
      featured: false
    }
  ];

  return (
    <section className="py-20">
      <div className="container-max section-padding">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-black mb-6">
            Featured Projects
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            A showcase of my recent work, from web applications to mobile apps and AI-powered solutions
          </p>
        </motion.div>

        {/* Projects Grid */}
        <div className="space-y-8">
          {projects.map((project, index) => {
            const IconComponent = project.icon;
            const isEven = index % 2 === 0;
            
            return (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 ${
                  isEven ? 'bg-black text-white' : 'bg-white text-black border border-gray-200'
                }`}
              >
                <div className="grid grid-cols-1 lg:grid-cols-2 min-h-[400px]">
                  {/* Content */}
                  <div className={`p-8 lg:p-12 flex flex-col justify-center ${
                    isEven ? 'lg:order-1' : 'lg:order-2'
                  }`}>
                    <div className="space-y-6">
                      {/* Project Type & Icon */}
                      <div className="flex items-center space-x-3">
                        <IconComponent 
                          size={24} 
                          className={isEven ? 'text-white' : 'text-black'} 
                        />
                        <span className={`text-sm font-medium ${
                          isEven ? 'text-gray-300' : 'text-gray-600'
                        }`}>
                          {project.type}
                        </span>
                        {project.featured && (
                          <span className="bg-yellow-400 text-black px-2 py-1 rounded-full text-xs font-medium">
                            Featured
                          </span>
                        )}
                      </div>

                      {/* Title */}
                      <h3 className="text-2xl md:text-3xl font-bold">
                        {project.title}
                      </h3>

                      {/* Description */}
                      <p className={`text-lg leading-relaxed ${
                        isEven ? 'text-gray-300' : 'text-gray-600'
                      }`}>
                        {project.description}
                      </p>

                      {/* Tags */}
                      <div className="flex flex-wrap gap-2">
                        {project.tags.map((tag, tagIndex) => (
                          <span
                            key={tagIndex}
                            className={`px-3 py-1 rounded-full text-sm font-medium ${
                              isEven 
                                ? 'bg-white bg-opacity-20 text-white' 
                                : 'bg-black bg-opacity-10 text-black'
                            }`}
                          >
                            {tag}
                          </span>
                        ))}
                      </div>

                      {/* Links */}
                      <div className="flex space-x-4 pt-4">
                        <a
                          href={project.github}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                            isEven
                              ? 'bg-white text-black hover:bg-gray-200'
                              : 'bg-black text-white hover:bg-gray-800'
                          }`}
                        >
                          <Github size={20} />
                          <span>Code</span>
                        </a>
                        <a
                          href={project.demo}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium border transition-all duration-300 ${
                            isEven
                              ? 'border-white text-white hover:bg-white hover:text-black'
                              : 'border-black text-black hover:bg-black hover:text-white'
                          }`}
                        >
                          <ExternalLink size={20} />
                          <span>Demo</span>
                        </a>
                      </div>
                    </div>
                  </div>

                  {/* Image Placeholder */}
                  <div className={`${
                    isEven ? 'lg:order-2' : 'lg:order-1'
                  } ${
                    isEven ? 'bg-gray-800' : 'bg-gray-100'
                  } flex items-center justify-center min-h-[300px] lg:min-h-[400px]`}>
                    <div className="text-center">
                      <IconComponent 
                        size={80} 
                        className={`mx-auto mb-4 ${
                          isEven ? 'text-gray-600' : 'text-gray-400'
                        }`} 
                      />
                      <p className={`text-lg font-medium ${
                        isEven ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        Project Screenshot
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* View More Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <a
            href="https://github.com/arkitkarmokar"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center space-x-2 bg-black text-white px-8 py-4 rounded-lg font-medium hover:bg-gray-800 transition-all duration-300 transform hover:scale-105"
          >
            <Github size={20} />
            <span>View All Projects</span>
          </a>
        </motion.div>
      </div>
    </section>
  );
};

export default ProjectsSection;
