# Arkit Karmokar (<PERSON>) - Portfolio Website

A modern, sleek developer portfolio website built with React, Vite, and Tailwind CSS. Features a black-and-white theme with smooth animations and responsive design.

## 🚀 Features

- **Modern Design**: Clean, minimal, and slightly futuristic black-and-white theme
- **Responsive Layout**: Optimized for desktop, tablet, and mobile devices
- **Smooth Animations**: Powered by Framer Motion for engaging user experience
- **Interactive Elements**: Hover effects, transitions, and micro-interactions
- **GitHub Activity**: Visual representation of coding activity
- **Skills Carousel**: Animated showcase of technical skills
- **Project Showcase**: Alternating layout for featured projects
- **Contact Form**: Functional contact form with validation
- **Performance Optimized**: Built with Vite for fast loading and development

## 🛠️ Tech Stack

- **Frontend**: React 18, Vite
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Routing**: React Router DOM
- **Font**: Inter (Google Fonts)

## 📱 Sections

1. **Hero Section**: Full viewport introduction with typewriter effect and GitHub calendar
2. **Skills Carousel**: Horizontal scrolling showcase of technical skills
3. **About Me**: Personal introduction with philosophy and highlights
4. **Projects**: Featured projects with alternating black/white cards
5. **Services**: Three-column layout for offered services
6. **Contact**: Contact form with social links and availability status

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd new-portfolio
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and visit `http://localhost:5173`

### Build for Production

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

## 🎨 Customization

### Colors
The portfolio uses a monochrome theme. To customize colors, edit the Tailwind configuration in `tailwind.config.js`.

### Content
Update the following files to customize content:
- `src/components/HeroSection.jsx` - Name, title, and introduction
- `src/components/AboutSection.jsx` - Personal bio and philosophy
- `src/components/ProjectsSection.jsx` - Project details and links
- `src/components/ServicesSection.jsx` - Service offerings
- `src/components/ContactSection.jsx` - Contact information

### Animations
Animations are handled by Framer Motion. Customize timing and effects in individual component files.

## 📁 Project Structure

```
src/
├── components/
│   ├── Layout.jsx          # Main layout with navigation
│   ├── HeroSection.jsx     # Hero section with intro
│   ├── SkillsCarousel.jsx  # Skills showcase
│   ├── AboutSection.jsx    # About me section
│   ├── ProjectsSection.jsx # Projects showcase
│   ├── ServicesSection.jsx # Services offered
│   ├── ContactSection.jsx  # Contact form
│   └── GitHubCalendar.jsx  # GitHub activity calendar
├── pages/
│   ├── Home.jsx           # Home page
│   ├── Projects.jsx       # Projects page
│   ├── Services.jsx       # Services page
│   └── Contact.jsx        # Contact page
├── App.jsx                # Main app component
├── main.jsx              # Entry point
└── index.css             # Global styles
```

## 🌟 Key Features Explained

### Sticky Navigation
- Vertical navigation positioned at top-left
- Smooth transitions between sections
- "Contact Now" CTA button

### GitHub Calendar
- Mock GitHub-style contribution calendar
- Animated squares with hover effects
- Activity statistics display

### Skills Carousel
- Infinite horizontal scroll animation
- Hover effects on skill cards
- Technology icons with labels

### Project Cards
- Alternating black/white theme
- Responsive grid layout
- GitHub and demo links
- Technology tags

### Contact Form
- Form validation
- Loading states
- Success feedback
- Social media links

## 📱 Responsive Design

The portfolio is fully responsive with breakpoints for:
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px

## 🔧 Performance Optimizations

- Lazy loading for images
- Code splitting with React Router
- Optimized animations with Framer Motion
- Minimal bundle size with Vite
- CSS optimization with Tailwind

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 👨‍💻 Author

**Arkit Karmokar (Maddy)**
- GitHub: [@arkitkarmokar](https://github.com/arkitkarmokar)
- LinkedIn: [Arkit Karmokar](https://linkedin.com/in/arkitkarmokar)
- Email: <EMAIL>

---

Built with ❤️ using React and modern web technologies.
