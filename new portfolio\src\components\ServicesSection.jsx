import { motion } from 'framer-motion';
import { Globe, Smartphone, Brain, ArrowRight, Check } from 'lucide-react';

const ServicesSection = () => {
  const services = [
    {
      id: 1,
      icon: Globe,
      title: "Website Development",
      description: "Modern, responsive websites and web applications built with cutting-edge technologies",
      features: [
        "Responsive Design",
        "Performance Optimization",
        "SEO Friendly",
        "Modern Frameworks",
        "E-commerce Solutions",
        "CMS Integration"
      ],
      technologies: ["React", "Next.js", "Node.js", "TypeScript", "Tailwind CSS"],
      price: "Starting at $2,000",
      popular: false
    },
    {
      id: 2,
      icon: Smartphone,
      title: "Android App Development",
      description: "Native Android applications with intuitive user interfaces and robust functionality",
      features: [
        "Native Performance",
        "Material Design",
        "Offline Capabilities",
        "Push Notifications",
        "API Integration",
        "Play Store Deployment"
      ],
      technologies: ["Kotlin", "Android SDK", "Firebase", "Room DB", "Retrofit"],
      price: "Starting at $3,500",
      popular: true
    },
    {
      id: 3,
      icon: Brain,
      title: "AI Agent Creation",
      description: "Intelligent AI agents and chatbots that automate tasks and enhance user experiences",
      features: [
        "Natural Language Processing",
        "Custom Training",
        "API Integration",
        "Multi-platform Support",
        "Analytics Dashboard",
        "Continuous Learning"
      ],
      technologies: ["Python", "TensorFlow", "OpenAI API", "LangChain", "FastAPI"],
      price: "Starting at $5,000",
      popular: false
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container-max section-padding">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-black mb-6">
            Services
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive digital solutions tailored to bring your ideas to life
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            
            return (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="group relative"
              >
                {/* Popular Badge */}
                {service.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <span className="bg-black text-white px-4 py-2 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className={`h-full bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 transform group-hover:-translate-y-2 ${
                  service.popular ? 'ring-2 ring-black' : ''
                }`}>
                  <div className="p-8 h-full flex flex-col">
                    {/* Icon & Title */}
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 bg-black rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <IconComponent size={32} className="text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-black mb-3">
                        {service.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {service.description}
                      </p>
                    </div>

                    {/* Features */}
                    <div className="flex-grow mb-6">
                      <h4 className="font-semibold text-black mb-4">What's Included:</h4>
                      <ul className="space-y-3">
                        {service.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center space-x-3">
                            <Check size={16} className="text-green-500 flex-shrink-0" />
                            <span className="text-gray-700 text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Technologies */}
                    <div className="mb-6">
                      <h4 className="font-semibold text-black mb-3">Technologies:</h4>
                      <div className="flex flex-wrap gap-2">
                        {service.technologies.map((tech, techIndex) => (
                          <span
                            key={techIndex}
                            className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Price & CTA */}
                    <div className="mt-auto">
                      <div className="text-center mb-6">
                        <p className="text-2xl font-bold text-black mb-2">
                          {service.price}
                        </p>
                        <p className="text-gray-500 text-sm">
                          Custom quotes available
                        </p>
                      </div>
                      
                      <button className="w-full bg-black text-white py-3 rounded-lg font-medium hover:bg-gray-800 transition-all duration-300 flex items-center justify-center space-x-2 group">
                        <span>Get Started</span>
                        <ArrowRight size={16} className="group-hover:translate-x-1 transition-transform duration-300" />
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-black text-white rounded-2xl p-8 md:p-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Have a Custom Project in Mind?
            </h3>
            <p className="text-gray-300 text-lg mb-8 max-w-2xl mx-auto">
              Every project is unique. Let's discuss your specific requirements and create a tailored solution that exceeds your expectations.
            </p>
            <button className="bg-white text-black px-8 py-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300 transform hover:scale-105">
              Schedule a Consultation
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ServicesSection;
