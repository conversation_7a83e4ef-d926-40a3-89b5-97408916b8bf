import { motion } from 'framer-motion';
import { 
  Code, 
  Database, 
  Globe, 
  Smartphone, 
  Brain, 
  Server,
  Palette,
  GitBranch,
  Terminal,
  Layers,
  Zap,
  Cloud
} from 'lucide-react';

const SkillsCarousel = () => {
  const skills = [
    { name: 'HTML', icon: Code, color: 'hover:text-orange-500' },
    { name: 'CSS', icon: Palette, color: 'hover:text-blue-500' },
    { name: 'JavaScript', icon: Zap, color: 'hover:text-yellow-500' },
    { name: 'React', icon: Globe, color: 'hover:text-cyan-500' },
    { name: 'Node.js', icon: Server, color: 'hover:text-green-500' },
    { name: 'TypeScript', icon: Code, color: 'hover:text-blue-600' },
    { name: 'Tail<PERSON>', icon: Palette, color: 'hover:text-teal-500' },
    { name: 'Express', icon: Server, color: 'hover:text-gray-600' },
    { name: 'PostgreSQL', icon: Database, color: 'hover:text-blue-700' },
    { name: 'MongoDB', icon: Database, color: 'hover:text-green-600' },
    { name: 'Python', icon: Terminal, color: 'hover:text-yellow-600' },
    { name: 'AI/ML', icon: Brain, color: 'hover:text-purple-500' },
    { name: 'Git', icon: GitBranch, color: 'hover:text-red-500' },
    { name: 'Docker', icon: Layers, color: 'hover:text-blue-400' },
    { name: 'AWS', icon: Cloud, color: 'hover:text-orange-400' },
    { name: 'Android', icon: Smartphone, color: 'hover:text-green-400' }
  ];

  // Duplicate skills for infinite scroll effect
  const duplicatedSkills = [...skills, ...skills];

  return (
    <section className="py-20 bg-black overflow-hidden">
      <div className="container-max section-padding">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Tech Stack
          </h2>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Technologies I use to bring ideas to life
          </p>
        </motion.div>

        {/* Skills Carousel */}
        <div className="relative">
          <motion.div
            className="flex space-x-8"
            animate={{
              x: [0, -50 * skills.length]
            }}
            transition={{
              x: {
                repeat: Infinity,
                repeatType: "loop",
                duration: 30,
                ease: "linear",
              },
            }}
          >
            {duplicatedSkills.map((skill, index) => {
              const IconComponent = skill.icon;
              return (
                <motion.div
                  key={`${skill.name}-${index}`}
                  className="flex-shrink-0 group"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="w-24 h-24 bg-white rounded-xl flex flex-col items-center justify-center space-y-2 group-hover:bg-gray-100 transition-all duration-300 shadow-lg group-hover:shadow-xl">
                    <IconComponent 
                      size={32} 
                      className={`text-black transition-colors duration-300 ${skill.color}`}
                    />
                    <span className="text-xs font-medium text-black">
                      {skill.name}
                    </span>
                  </div>
                </motion.div>
              );
            })}
          </motion.div>

          {/* Gradient Overlays */}
          <div className="absolute top-0 left-0 w-32 h-full bg-gradient-to-r from-black to-transparent z-10" />
          <div className="absolute top-0 right-0 w-32 h-full bg-gradient-to-l from-black to-transparent z-10" />
        </div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="text-gray-400 text-lg">
            Always learning, always growing. Currently exploring{' '}
            <span className="text-white font-medium">Next.js</span> and{' '}
            <span className="text-white font-medium">Machine Learning</span>
          </p>
        </motion.div>
      </div>
    </section>
  );
};

export default SkillsCarousel;
