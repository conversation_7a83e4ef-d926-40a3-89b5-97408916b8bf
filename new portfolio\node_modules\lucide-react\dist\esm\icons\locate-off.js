/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 19v3", key: "npa21l" }],
  ["path", { d: "M12 2v3", key: "qbqxhf" }],
  ["path", { d: "M18.89 13.24a7 7 0 0 0-8.13-8.13", key: "1v9jrh" }],
  ["path", { d: "M19 12h3", key: "osuazr" }],
  ["path", { d: "M2 12h3", key: "1wrr53" }],
  ["path", { d: "m2 2 20 20", key: "1ooewy" }],
  ["path", { d: "M7.05 7.05a7 7 0 0 0 9.9 9.9", key: "rc5l2e" }]
];
const LocateOff = createLucideIcon("locate-off", __iconNode);

export { __iconNode, LocateOff as default };
//# sourceMappingURL=locate-off.js.map
