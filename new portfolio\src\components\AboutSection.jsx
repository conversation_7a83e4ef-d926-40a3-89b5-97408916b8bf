import { motion } from 'framer-motion';
import { Code, Heart, Lightbulb, Target } from 'lucide-react';

const AboutSection = () => {
  const highlights = [
    {
      icon: Code,
      title: "Full-Stack Developer",
      description: "Building end-to-end solutions with modern technologies"
    },
    {
      icon: Heart,
      title: "Passion-Driven",
      description: "Love turning complex problems into elegant solutions"
    },
    {
      icon: Lightbulb,
      title: "Creative Thinker",
      description: "Always exploring new ways to innovate and improve"
    },
    {
      icon: Target,
      title: "Goal-Oriented",
      description: "Focused on delivering results that make a difference"
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container-max section-padding">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div>
              <motion.h2
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
                className="text-4xl md:text-5xl font-bold text-black mb-6"
              >
                About Me
              </motion.h2>
              
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="space-y-6 text-lg text-gray-700 leading-relaxed"
              >
                <p>
                  Hi, I'm <span className="font-semibold text-black">Arkit (Maddy)</span> — a full-stack developer passionate about building creative digital experiences and AI agents that think.
                </p>
                
                <p>
                  My journey in tech is driven by curiosity and the belief that great software should not only solve problems but also inspire and delight users. I love the challenge of turning complex ideas into clean, functional code.
                </p>
                
                <p>
                  When I'm not coding, you'll find me exploring the latest in AI and machine learning, contributing to open-source projects, or experimenting with new frameworks and technologies.
                </p>
              </motion.div>
            </div>

            {/* Philosophy */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              viewport={{ once: true }}
              className="bg-gray-50 p-6 rounded-lg border-l-4 border-black"
            >
              <h3 className="text-xl font-semibold text-black mb-3">My Philosophy</h3>
              <p className="text-gray-700 italic">
                "Code is poetry written in logic. Every line should serve a purpose, every function should tell a story, and every project should make the world a little bit better."
              </p>
            </motion.div>
          </motion.div>

          {/* Right Content */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Avatar Placeholder */}
            <div className="flex justify-center lg:justify-start mb-8">
              <div className="w-64 h-64 bg-gradient-to-br from-gray-200 to-gray-300 rounded-2xl flex items-center justify-center shadow-lg">
                <div className="text-center">
                  <div className="w-20 h-20 bg-black rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span className="text-white text-2xl font-bold">AK</span>
                  </div>
                  <p className="text-gray-600 font-medium">Arkit Karmokar</p>
                  <p className="text-gray-500 text-sm">aka Maddy</p>
                </div>
              </div>
            </div>

            {/* Highlights Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {highlights.map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.1 * index }}
                    viewport={{ once: true }}
                    className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-300 group"
                  >
                    <IconComponent 
                      size={32} 
                      className="text-black mb-4 group-hover:scale-110 transition-transform duration-300" 
                    />
                    <h4 className="font-semibold text-black mb-2">{item.title}</h4>
                    <p className="text-gray-600 text-sm">{item.description}</p>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
