/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 2v10", key: "mnfbl" }],
  ["path", { d: "m8.5 4 7 4", key: "m1xjk3" }],
  ["path", { d: "m8.5 8 7-4", key: "t0m5j6" }],
  ["circle", { cx: "12", cy: "17", r: "5", key: "qbz8iq" }]
];
const NonBinary = createLucideIcon("non-binary", __iconNode);

export { __iconNode, NonBinary as default };
//# sourceMappingURL=non-binary.js.map
