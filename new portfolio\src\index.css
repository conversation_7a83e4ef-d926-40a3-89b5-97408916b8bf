@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', system-ui, sans-serif;
  background-color: white;
  color: black;
}

html {
  scroll-behavior: smooth;
}

.nav-link {
  color: black;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #6b7280;
}

.nav-link.active {
  color: #1f2937;
  font-weight: 600;
}

.btn-primary {
  background-color: black;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: #374151;
}

.btn-secondary {
  border: 1px solid black;
  color: black;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background-color: black;
  color: white;
}

.section-padding {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

@media (min-width: 768px) {
  .section-padding {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding-left: 6rem;
    padding-right: 6rem;
  }
}

.container-max {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
}
