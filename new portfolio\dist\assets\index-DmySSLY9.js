(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))r(c);new MutationObserver(c=>{for(const f of c)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&r(d)}).observe(document,{childList:!0,subtree:!0});function s(c){const f={};return c.integrity&&(f.integrity=c.integrity),c.referrerPolicy&&(f.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?f.credentials="include":c.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function r(c){if(c.ep)return;c.ep=!0;const f=s(c);fetch(c.href,f)}})();var qu={exports:{}},hl={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cp;function ax(){if(cp)return hl;cp=1;var a=Symbol.for("react.transitional.element"),i=Symbol.for("react.fragment");function s(r,c,f){var d=null;if(f!==void 0&&(d=""+f),c.key!==void 0&&(d=""+c.key),"key"in c){f={};for(var p in c)p!=="key"&&(f[p]=c[p])}else f=c;return c=f.ref,{$$typeof:a,type:r,key:d,ref:c!==void 0?c:null,props:f}}return hl.Fragment=i,hl.jsx=s,hl.jsxs=s,hl}var fp;function ix(){return fp||(fp=1,qu.exports=ax()),qu.exports}var S=ix(),Yu={exports:{}},st={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dp;function lx(){if(dp)return st;dp=1;var a=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),x=Symbol.iterator;function b(A){return A===null||typeof A!="object"?null:(A=x&&A[x]||A["@@iterator"],typeof A=="function"?A:null)}var M={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},D=Object.assign,z={};function B(A,Y,Z){this.props=A,this.context=Y,this.refs=z,this.updater=Z||M}B.prototype.isReactComponent={},B.prototype.setState=function(A,Y){if(typeof A!="object"&&typeof A!="function"&&A!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,A,Y,"setState")},B.prototype.forceUpdate=function(A){this.updater.enqueueForceUpdate(this,A,"forceUpdate")};function V(){}V.prototype=B.prototype;function q(A,Y,Z){this.props=A,this.context=Y,this.refs=z,this.updater=Z||M}var k=q.prototype=new V;k.constructor=q,D(k,B.prototype),k.isPureReactComponent=!0;var P=Array.isArray,G={H:null,A:null,T:null,S:null,V:null},at=Object.prototype.hasOwnProperty;function lt(A,Y,Z,K,W,mt){return Z=mt.ref,{$$typeof:a,type:A,key:Y,ref:Z!==void 0?Z:null,props:mt}}function J(A,Y){return lt(A.type,Y,void 0,void 0,void 0,A.props)}function rt(A){return typeof A=="object"&&A!==null&&A.$$typeof===a}function bt(A){var Y={"=":"=0",":":"=2"};return"$"+A.replace(/[=:]/g,function(Z){return Y[Z]})}var Yt=/\/+/g;function Gt(A,Y){return typeof A=="object"&&A!==null&&A.key!=null?bt(""+A.key):Y.toString(36)}function Fe(){}function He(A){switch(A.status){case"fulfilled":return A.value;case"rejected":throw A.reason;default:switch(typeof A.status=="string"?A.then(Fe,Fe):(A.status="pending",A.then(function(Y){A.status==="pending"&&(A.status="fulfilled",A.value=Y)},function(Y){A.status==="pending"&&(A.status="rejected",A.reason=Y)})),A.status){case"fulfilled":return A.value;case"rejected":throw A.reason}}throw A}function Jt(A,Y,Z,K,W){var mt=typeof A;(mt==="undefined"||mt==="boolean")&&(A=null);var it=!1;if(A===null)it=!0;else switch(mt){case"bigint":case"string":case"number":it=!0;break;case"object":switch(A.$$typeof){case a:case i:it=!0;break;case g:return it=A._init,Jt(it(A._payload),Y,Z,K,W)}}if(it)return W=W(A),it=K===""?"."+Gt(A,0):K,P(W)?(Z="",it!=null&&(Z=it.replace(Yt,"$&/")+"/"),Jt(W,Y,Z,"",function(xn){return xn})):W!=null&&(rt(W)&&(W=J(W,Z+(W.key==null||A&&A.key===W.key?"":(""+W.key).replace(Yt,"$&/")+"/")+it)),Y.push(W)),1;it=0;var he=K===""?".":K+":";if(P(A))for(var Dt=0;Dt<A.length;Dt++)K=A[Dt],mt=he+Gt(K,Dt),it+=Jt(K,Y,Z,mt,W);else if(Dt=b(A),typeof Dt=="function")for(A=Dt.call(A),Dt=0;!(K=A.next()).done;)K=K.value,mt=he+Gt(K,Dt++),it+=Jt(K,Y,Z,mt,W);else if(mt==="object"){if(typeof A.then=="function")return Jt(He(A),Y,Z,K,W);throw Y=String(A),Error("Objects are not valid as a React child (found: "+(Y==="[object Object]"?"object with keys {"+Object.keys(A).join(", ")+"}":Y)+"). If you meant to render a collection of children, use an array instead.")}return it}function U(A,Y,Z){if(A==null)return A;var K=[],W=0;return Jt(A,K,"","",function(mt){return Y.call(Z,mt,W++)}),K}function X(A){if(A._status===-1){var Y=A._result;Y=Y(),Y.then(function(Z){(A._status===0||A._status===-1)&&(A._status=1,A._result=Z)},function(Z){(A._status===0||A._status===-1)&&(A._status=2,A._result=Z)}),A._status===-1&&(A._status=0,A._result=Y)}if(A._status===1)return A._result.default;throw A._result}var $=typeof reportError=="function"?reportError:function(A){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Y=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof A=="object"&&A!==null&&typeof A.message=="string"?String(A.message):String(A),error:A});if(!window.dispatchEvent(Y))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",A);return}console.error(A)};function yt(){}return st.Children={map:U,forEach:function(A,Y,Z){U(A,function(){Y.apply(this,arguments)},Z)},count:function(A){var Y=0;return U(A,function(){Y++}),Y},toArray:function(A){return U(A,function(Y){return Y})||[]},only:function(A){if(!rt(A))throw Error("React.Children.only expected to receive a single React element child.");return A}},st.Component=B,st.Fragment=s,st.Profiler=c,st.PureComponent=q,st.StrictMode=r,st.Suspense=m,st.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=G,st.__COMPILER_RUNTIME={__proto__:null,c:function(A){return G.H.useMemoCache(A)}},st.cache=function(A){return function(){return A.apply(null,arguments)}},st.cloneElement=function(A,Y,Z){if(A==null)throw Error("The argument must be a React element, but you passed "+A+".");var K=D({},A.props),W=A.key,mt=void 0;if(Y!=null)for(it in Y.ref!==void 0&&(mt=void 0),Y.key!==void 0&&(W=""+Y.key),Y)!at.call(Y,it)||it==="key"||it==="__self"||it==="__source"||it==="ref"&&Y.ref===void 0||(K[it]=Y[it]);var it=arguments.length-2;if(it===1)K.children=Z;else if(1<it){for(var he=Array(it),Dt=0;Dt<it;Dt++)he[Dt]=arguments[Dt+2];K.children=he}return lt(A.type,W,void 0,void 0,mt,K)},st.createContext=function(A){return A={$$typeof:d,_currentValue:A,_currentValue2:A,_threadCount:0,Provider:null,Consumer:null},A.Provider=A,A.Consumer={$$typeof:f,_context:A},A},st.createElement=function(A,Y,Z){var K,W={},mt=null;if(Y!=null)for(K in Y.key!==void 0&&(mt=""+Y.key),Y)at.call(Y,K)&&K!=="key"&&K!=="__self"&&K!=="__source"&&(W[K]=Y[K]);var it=arguments.length-2;if(it===1)W.children=Z;else if(1<it){for(var he=Array(it),Dt=0;Dt<it;Dt++)he[Dt]=arguments[Dt+2];W.children=he}if(A&&A.defaultProps)for(K in it=A.defaultProps,it)W[K]===void 0&&(W[K]=it[K]);return lt(A,mt,void 0,void 0,null,W)},st.createRef=function(){return{current:null}},st.forwardRef=function(A){return{$$typeof:p,render:A}},st.isValidElement=rt,st.lazy=function(A){return{$$typeof:g,_payload:{_status:-1,_result:A},_init:X}},st.memo=function(A,Y){return{$$typeof:h,type:A,compare:Y===void 0?null:Y}},st.startTransition=function(A){var Y=G.T,Z={};G.T=Z;try{var K=A(),W=G.S;W!==null&&W(Z,K),typeof K=="object"&&K!==null&&typeof K.then=="function"&&K.then(yt,$)}catch(mt){$(mt)}finally{G.T=Y}},st.unstable_useCacheRefresh=function(){return G.H.useCacheRefresh()},st.use=function(A){return G.H.use(A)},st.useActionState=function(A,Y,Z){return G.H.useActionState(A,Y,Z)},st.useCallback=function(A,Y){return G.H.useCallback(A,Y)},st.useContext=function(A){return G.H.useContext(A)},st.useDebugValue=function(){},st.useDeferredValue=function(A,Y){return G.H.useDeferredValue(A,Y)},st.useEffect=function(A,Y,Z){var K=G.H;if(typeof Z=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return K.useEffect(A,Y)},st.useId=function(){return G.H.useId()},st.useImperativeHandle=function(A,Y,Z){return G.H.useImperativeHandle(A,Y,Z)},st.useInsertionEffect=function(A,Y){return G.H.useInsertionEffect(A,Y)},st.useLayoutEffect=function(A,Y){return G.H.useLayoutEffect(A,Y)},st.useMemo=function(A,Y){return G.H.useMemo(A,Y)},st.useOptimistic=function(A,Y){return G.H.useOptimistic(A,Y)},st.useReducer=function(A,Y,Z){return G.H.useReducer(A,Y,Z)},st.useRef=function(A){return G.H.useRef(A)},st.useState=function(A){return G.H.useState(A)},st.useSyncExternalStore=function(A,Y,Z){return G.H.useSyncExternalStore(A,Y,Z)},st.useTransition=function(){return G.H.useTransition()},st.version="19.1.0",st}var hp;function Oc(){return hp||(hp=1,Yu.exports=lx()),Yu.exports}var C=Oc(),Gu={exports:{}},ml={},Xu={exports:{}},Ku={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mp;function sx(){return mp||(mp=1,function(a){function i(U,X){var $=U.length;U.push(X);t:for(;0<$;){var yt=$-1>>>1,A=U[yt];if(0<c(A,X))U[yt]=X,U[$]=A,$=yt;else break t}}function s(U){return U.length===0?null:U[0]}function r(U){if(U.length===0)return null;var X=U[0],$=U.pop();if($!==X){U[0]=$;t:for(var yt=0,A=U.length,Y=A>>>1;yt<Y;){var Z=2*(yt+1)-1,K=U[Z],W=Z+1,mt=U[W];if(0>c(K,$))W<A&&0>c(mt,K)?(U[yt]=mt,U[W]=$,yt=W):(U[yt]=K,U[Z]=$,yt=Z);else if(W<A&&0>c(mt,$))U[yt]=mt,U[W]=$,yt=W;else break t}}return X}function c(U,X){var $=U.sortIndex-X.sortIndex;return $!==0?$:U.id-X.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;a.unstable_now=function(){return f.now()}}else{var d=Date,p=d.now();a.unstable_now=function(){return d.now()-p}}var m=[],h=[],g=1,x=null,b=3,M=!1,D=!1,z=!1,B=!1,V=typeof setTimeout=="function"?setTimeout:null,q=typeof clearTimeout=="function"?clearTimeout:null,k=typeof setImmediate<"u"?setImmediate:null;function P(U){for(var X=s(h);X!==null;){if(X.callback===null)r(h);else if(X.startTime<=U)r(h),X.sortIndex=X.expirationTime,i(m,X);else break;X=s(h)}}function G(U){if(z=!1,P(U),!D)if(s(m)!==null)D=!0,at||(at=!0,Gt());else{var X=s(h);X!==null&&Jt(G,X.startTime-U)}}var at=!1,lt=-1,J=5,rt=-1;function bt(){return B?!0:!(a.unstable_now()-rt<J)}function Yt(){if(B=!1,at){var U=a.unstable_now();rt=U;var X=!0;try{t:{D=!1,z&&(z=!1,q(lt),lt=-1),M=!0;var $=b;try{e:{for(P(U),x=s(m);x!==null&&!(x.expirationTime>U&&bt());){var yt=x.callback;if(typeof yt=="function"){x.callback=null,b=x.priorityLevel;var A=yt(x.expirationTime<=U);if(U=a.unstable_now(),typeof A=="function"){x.callback=A,P(U),X=!0;break e}x===s(m)&&r(m),P(U)}else r(m);x=s(m)}if(x!==null)X=!0;else{var Y=s(h);Y!==null&&Jt(G,Y.startTime-U),X=!1}}break t}finally{x=null,b=$,M=!1}X=void 0}}finally{X?Gt():at=!1}}}var Gt;if(typeof k=="function")Gt=function(){k(Yt)};else if(typeof MessageChannel<"u"){var Fe=new MessageChannel,He=Fe.port2;Fe.port1.onmessage=Yt,Gt=function(){He.postMessage(null)}}else Gt=function(){V(Yt,0)};function Jt(U,X){lt=V(function(){U(a.unstable_now())},X)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(U){U.callback=null},a.unstable_forceFrameRate=function(U){0>U||125<U?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):J=0<U?Math.floor(1e3/U):5},a.unstable_getCurrentPriorityLevel=function(){return b},a.unstable_next=function(U){switch(b){case 1:case 2:case 3:var X=3;break;default:X=b}var $=b;b=X;try{return U()}finally{b=$}},a.unstable_requestPaint=function(){B=!0},a.unstable_runWithPriority=function(U,X){switch(U){case 1:case 2:case 3:case 4:case 5:break;default:U=3}var $=b;b=U;try{return X()}finally{b=$}},a.unstable_scheduleCallback=function(U,X,$){var yt=a.unstable_now();switch(typeof $=="object"&&$!==null?($=$.delay,$=typeof $=="number"&&0<$?yt+$:yt):$=yt,U){case 1:var A=-1;break;case 2:A=250;break;case 5:A=1073741823;break;case 4:A=1e4;break;default:A=5e3}return A=$+A,U={id:g++,callback:X,priorityLevel:U,startTime:$,expirationTime:A,sortIndex:-1},$>yt?(U.sortIndex=$,i(h,U),s(m)===null&&U===s(h)&&(z?(q(lt),lt=-1):z=!0,Jt(G,$-yt))):(U.sortIndex=A,i(m,U),D||M||(D=!0,at||(at=!0,Gt()))),U},a.unstable_shouldYield=bt,a.unstable_wrapCallback=function(U){var X=b;return function(){var $=b;b=X;try{return U.apply(this,arguments)}finally{b=$}}}}(Ku)),Ku}var pp;function rx(){return pp||(pp=1,Xu.exports=sx()),Xu.exports}var Zu={exports:{}},ae={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yp;function ox(){if(yp)return ae;yp=1;var a=Oc();function i(m){var h="https://react.dev/errors/"+m;if(1<arguments.length){h+="?args[]="+encodeURIComponent(arguments[1]);for(var g=2;g<arguments.length;g++)h+="&args[]="+encodeURIComponent(arguments[g])}return"Minified React error #"+m+"; visit "+h+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var r={d:{f:s,r:function(){throw Error(i(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},c=Symbol.for("react.portal");function f(m,h,g){var x=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:c,key:x==null?null:""+x,children:m,containerInfo:h,implementation:g}}var d=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(m,h){if(m==="font")return"";if(typeof h=="string")return h==="use-credentials"?h:""}return ae.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,ae.createPortal=function(m,h){var g=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!h||h.nodeType!==1&&h.nodeType!==9&&h.nodeType!==11)throw Error(i(299));return f(m,h,null,g)},ae.flushSync=function(m){var h=d.T,g=r.p;try{if(d.T=null,r.p=2,m)return m()}finally{d.T=h,r.p=g,r.d.f()}},ae.preconnect=function(m,h){typeof m=="string"&&(h?(h=h.crossOrigin,h=typeof h=="string"?h==="use-credentials"?h:"":void 0):h=null,r.d.C(m,h))},ae.prefetchDNS=function(m){typeof m=="string"&&r.d.D(m)},ae.preinit=function(m,h){if(typeof m=="string"&&h&&typeof h.as=="string"){var g=h.as,x=p(g,h.crossOrigin),b=typeof h.integrity=="string"?h.integrity:void 0,M=typeof h.fetchPriority=="string"?h.fetchPriority:void 0;g==="style"?r.d.S(m,typeof h.precedence=="string"?h.precedence:void 0,{crossOrigin:x,integrity:b,fetchPriority:M}):g==="script"&&r.d.X(m,{crossOrigin:x,integrity:b,fetchPriority:M,nonce:typeof h.nonce=="string"?h.nonce:void 0})}},ae.preinitModule=function(m,h){if(typeof m=="string")if(typeof h=="object"&&h!==null){if(h.as==null||h.as==="script"){var g=p(h.as,h.crossOrigin);r.d.M(m,{crossOrigin:g,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0})}}else h==null&&r.d.M(m)},ae.preload=function(m,h){if(typeof m=="string"&&typeof h=="object"&&h!==null&&typeof h.as=="string"){var g=h.as,x=p(g,h.crossOrigin);r.d.L(m,g,{crossOrigin:x,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0,type:typeof h.type=="string"?h.type:void 0,fetchPriority:typeof h.fetchPriority=="string"?h.fetchPriority:void 0,referrerPolicy:typeof h.referrerPolicy=="string"?h.referrerPolicy:void 0,imageSrcSet:typeof h.imageSrcSet=="string"?h.imageSrcSet:void 0,imageSizes:typeof h.imageSizes=="string"?h.imageSizes:void 0,media:typeof h.media=="string"?h.media:void 0})}},ae.preloadModule=function(m,h){if(typeof m=="string")if(h){var g=p(h.as,h.crossOrigin);r.d.m(m,{as:typeof h.as=="string"&&h.as!=="script"?h.as:void 0,crossOrigin:g,integrity:typeof h.integrity=="string"?h.integrity:void 0})}else r.d.m(m)},ae.requestFormReset=function(m){r.d.r(m)},ae.unstable_batchedUpdates=function(m,h){return m(h)},ae.useFormState=function(m,h,g){return d.H.useFormState(m,h,g)},ae.useFormStatus=function(){return d.H.useHostTransitionStatus()},ae.version="19.1.0",ae}var gp;function ux(){if(gp)return Zu.exports;gp=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(i){console.error(i)}}return a(),Zu.exports=ox(),Zu.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vp;function cx(){if(vp)return ml;vp=1;var a=rx(),i=Oc(),s=ux();function r(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function f(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function d(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function p(t){if(f(t)!==t)throw Error(r(188))}function m(t){var e=t.alternate;if(!e){if(e=f(t),e===null)throw Error(r(188));return e!==t?null:t}for(var n=t,l=e;;){var o=n.return;if(o===null)break;var u=o.alternate;if(u===null){if(l=o.return,l!==null){n=l;continue}break}if(o.child===u.child){for(u=o.child;u;){if(u===n)return p(o),t;if(u===l)return p(o),e;u=u.sibling}throw Error(r(188))}if(n.return!==l.return)n=o,l=u;else{for(var y=!1,v=o.child;v;){if(v===n){y=!0,n=o,l=u;break}if(v===l){y=!0,l=o,n=u;break}v=v.sibling}if(!y){for(v=u.child;v;){if(v===n){y=!0,n=u,l=o;break}if(v===l){y=!0,l=u,n=o;break}v=v.sibling}if(!y)throw Error(r(189))}}if(n.alternate!==l)throw Error(r(190))}if(n.tag!==3)throw Error(r(188));return n.stateNode.current===n?t:e}function h(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=h(t),e!==null)return e;t=t.sibling}return null}var g=Object.assign,x=Symbol.for("react.element"),b=Symbol.for("react.transitional.element"),M=Symbol.for("react.portal"),D=Symbol.for("react.fragment"),z=Symbol.for("react.strict_mode"),B=Symbol.for("react.profiler"),V=Symbol.for("react.provider"),q=Symbol.for("react.consumer"),k=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),G=Symbol.for("react.suspense"),at=Symbol.for("react.suspense_list"),lt=Symbol.for("react.memo"),J=Symbol.for("react.lazy"),rt=Symbol.for("react.activity"),bt=Symbol.for("react.memo_cache_sentinel"),Yt=Symbol.iterator;function Gt(t){return t===null||typeof t!="object"?null:(t=Yt&&t[Yt]||t["@@iterator"],typeof t=="function"?t:null)}var Fe=Symbol.for("react.client.reference");function He(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Fe?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case D:return"Fragment";case B:return"Profiler";case z:return"StrictMode";case G:return"Suspense";case at:return"SuspenseList";case rt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case M:return"Portal";case k:return(t.displayName||"Context")+".Provider";case q:return(t._context.displayName||"Context")+".Consumer";case P:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case lt:return e=t.displayName||null,e!==null?e:He(t.type)||"Memo";case J:e=t._payload,t=t._init;try{return He(t(e))}catch{}}return null}var Jt=Array.isArray,U=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,X=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,$={pending:!1,data:null,method:null,action:null},yt=[],A=-1;function Y(t){return{current:t}}function Z(t){0>A||(t.current=yt[A],yt[A]=null,A--)}function K(t,e){A++,yt[A]=t.current,t.current=e}var W=Y(null),mt=Y(null),it=Y(null),he=Y(null);function Dt(t,e){switch(K(it,e),K(mt,t),K(W,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Lm(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Lm(e),t=Hm(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Z(W),K(W,t)}function xn(){Z(W),Z(mt),Z(it)}function Mr(t){t.memoizedState!==null&&K(he,t);var e=W.current,n=Hm(e,t.type);e!==n&&(K(mt,t),K(W,n))}function ql(t){mt.current===t&&(Z(W),Z(mt)),he.current===t&&(Z(he),ol._currentValue=$)}var wr=Object.prototype.hasOwnProperty,Dr=a.unstable_scheduleCallback,Rr=a.unstable_cancelCallback,_g=a.unstable_shouldYield,Ug=a.unstable_requestPaint,ke=a.unstable_now,Bg=a.unstable_getCurrentPriorityLevel,vf=a.unstable_ImmediatePriority,xf=a.unstable_UserBlockingPriority,Yl=a.unstable_NormalPriority,Lg=a.unstable_LowPriority,bf=a.unstable_IdlePriority,Hg=a.log,kg=a.unstable_setDisableYieldValue,yi=null,me=null;function bn(t){if(typeof Hg=="function"&&kg(t),me&&typeof me.setStrictMode=="function")try{me.setStrictMode(yi,t)}catch{}}var pe=Math.clz32?Math.clz32:Gg,qg=Math.log,Yg=Math.LN2;function Gg(t){return t>>>=0,t===0?32:31-(qg(t)/Yg|0)|0}var Gl=256,Xl=4194304;function $n(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Kl(t,e,n){var l=t.pendingLanes;if(l===0)return 0;var o=0,u=t.suspendedLanes,y=t.pingedLanes;t=t.warmLanes;var v=l&134217727;return v!==0?(l=v&~u,l!==0?o=$n(l):(y&=v,y!==0?o=$n(y):n||(n=v&~t,n!==0&&(o=$n(n))))):(v=l&~u,v!==0?o=$n(v):y!==0?o=$n(y):n||(n=l&~t,n!==0&&(o=$n(n)))),o===0?0:e!==0&&e!==o&&(e&u)===0&&(u=o&-o,n=e&-e,u>=n||u===32&&(n&4194048)!==0)?e:o}function gi(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Xg(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Sf(){var t=Gl;return Gl<<=1,(Gl&4194048)===0&&(Gl=256),t}function Tf(){var t=Xl;return Xl<<=1,(Xl&62914560)===0&&(Xl=4194304),t}function Cr(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function vi(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Kg(t,e,n,l,o,u){var y=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var v=t.entanglements,T=t.expirationTimes,N=t.hiddenUpdates;for(n=y&~n;0<n;){var _=31-pe(n),H=1<<_;v[_]=0,T[_]=-1;var j=N[_];if(j!==null)for(N[_]=null,_=0;_<j.length;_++){var O=j[_];O!==null&&(O.lane&=-536870913)}n&=~H}l!==0&&Af(t,l,0),u!==0&&o===0&&t.tag!==0&&(t.suspendedLanes|=u&~(y&~e))}function Af(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-pe(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194090}function Ef(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-pe(n),o=1<<l;o&e|t[l]&e&&(t[l]|=e),n&=~o}}function Nr(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function jr(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Mf(){var t=X.p;return t!==0?t:(t=window.event,t===void 0?32:ip(t.type))}function Zg(t,e){var n=X.p;try{return X.p=t,e()}finally{X.p=n}}var Sn=Math.random().toString(36).slice(2),ee="__reactFiber$"+Sn,re="__reactProps$"+Sn,xa="__reactContainer$"+Sn,Or="__reactEvents$"+Sn,Qg="__reactListeners$"+Sn,Pg="__reactHandles$"+Sn,wf="__reactResources$"+Sn,xi="__reactMarker$"+Sn;function Vr(t){delete t[ee],delete t[re],delete t[Or],delete t[Qg],delete t[Pg]}function ba(t){var e=t[ee];if(e)return e;for(var n=t.parentNode;n;){if(e=n[xa]||n[ee]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Gm(t);t!==null;){if(n=t[ee])return n;t=Gm(t)}return e}t=n,n=t.parentNode}return null}function Sa(t){if(t=t[ee]||t[xa]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function bi(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(r(33))}function Ta(t){var e=t[wf];return e||(e=t[wf]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Zt(t){t[xi]=!0}var Df=new Set,Rf={};function Jn(t,e){Aa(t,e),Aa(t+"Capture",e)}function Aa(t,e){for(Rf[t]=e,t=0;t<e.length;t++)Df.add(e[t])}var $g=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Cf={},Nf={};function Jg(t){return wr.call(Nf,t)?!0:wr.call(Cf,t)?!1:$g.test(t)?Nf[t]=!0:(Cf[t]=!0,!1)}function Zl(t,e,n){if(Jg(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Ql(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function We(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}var zr,jf;function Ea(t){if(zr===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);zr=e&&e[1]||"",jf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+zr+t+jf}var _r=!1;function Ur(t,e){if(!t||_r)return"";_r=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var H=function(){throw Error()};if(Object.defineProperty(H.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(H,[])}catch(O){var j=O}Reflect.construct(t,[],H)}else{try{H.call()}catch(O){j=O}t.call(H.prototype)}}else{try{throw Error()}catch(O){j=O}(H=t())&&typeof H.catch=="function"&&H.catch(function(){})}}catch(O){if(O&&j&&typeof O.stack=="string")return[O.stack,j.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=l.DetermineComponentFrameRoot(),y=u[0],v=u[1];if(y&&v){var T=y.split(`
`),N=v.split(`
`);for(o=l=0;l<T.length&&!T[l].includes("DetermineComponentFrameRoot");)l++;for(;o<N.length&&!N[o].includes("DetermineComponentFrameRoot");)o++;if(l===T.length||o===N.length)for(l=T.length-1,o=N.length-1;1<=l&&0<=o&&T[l]!==N[o];)o--;for(;1<=l&&0<=o;l--,o--)if(T[l]!==N[o]){if(l!==1||o!==1)do if(l--,o--,0>o||T[l]!==N[o]){var _=`
`+T[l].replace(" at new "," at ");return t.displayName&&_.includes("<anonymous>")&&(_=_.replace("<anonymous>",t.displayName)),_}while(1<=l&&0<=o);break}}}finally{_r=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?Ea(n):""}function Fg(t){switch(t.tag){case 26:case 27:case 5:return Ea(t.type);case 16:return Ea("Lazy");case 13:return Ea("Suspense");case 19:return Ea("SuspenseList");case 0:case 15:return Ur(t.type,!1);case 11:return Ur(t.type.render,!1);case 1:return Ur(t.type,!0);case 31:return Ea("Activity");default:return""}}function Of(t){try{var e="";do e+=Fg(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Ae(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Vf(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Wg(t){var e=Vf(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,u=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(y){l=""+y,u.call(this,y)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(y){l=""+y},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Pl(t){t._valueTracker||(t._valueTracker=Wg(t))}function zf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=Vf(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function $l(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var Ig=/[\n"\\]/g;function Ee(t){return t.replace(Ig,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Br(t,e,n,l,o,u,y,v){t.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?t.type=y:t.removeAttribute("type"),e!=null?y==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Ae(e)):t.value!==""+Ae(e)&&(t.value=""+Ae(e)):y!=="submit"&&y!=="reset"||t.removeAttribute("value"),e!=null?Lr(t,y,Ae(e)):n!=null?Lr(t,y,Ae(n)):l!=null&&t.removeAttribute("value"),o==null&&u!=null&&(t.defaultChecked=!!u),o!=null&&(t.checked=o&&typeof o!="function"&&typeof o!="symbol"),v!=null&&typeof v!="function"&&typeof v!="symbol"&&typeof v!="boolean"?t.name=""+Ae(v):t.removeAttribute("name")}function _f(t,e,n,l,o,u,y,v){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||n!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;n=n!=null?""+Ae(n):"",e=e!=null?""+Ae(e):n,v||e===t.value||(t.value=e),t.defaultValue=e}l=l??o,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=v?t.checked:!!l,t.defaultChecked=!!l,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(t.name=y)}function Lr(t,e,n){e==="number"&&$l(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function Ma(t,e,n,l){if(t=t.options,e){e={};for(var o=0;o<n.length;o++)e["$"+n[o]]=!0;for(n=0;n<t.length;n++)o=e.hasOwnProperty("$"+t[n].value),t[n].selected!==o&&(t[n].selected=o),o&&l&&(t[n].defaultSelected=!0)}else{for(n=""+Ae(n),e=null,o=0;o<t.length;o++){if(t[o].value===n){t[o].selected=!0,l&&(t[o].defaultSelected=!0);return}e!==null||t[o].disabled||(e=t[o])}e!==null&&(e.selected=!0)}}function Uf(t,e,n){if(e!=null&&(e=""+Ae(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+Ae(n):""}function Bf(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(r(92));if(Jt(l)){if(1<l.length)throw Error(r(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=Ae(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function wa(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var tv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Lf(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||tv.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function Hf(t,e,n){if(e!=null&&typeof e!="object")throw Error(r(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var o in e)l=e[o],e.hasOwnProperty(o)&&n[o]!==l&&Lf(t,o,l)}else for(var u in e)e.hasOwnProperty(u)&&Lf(t,u,e[u])}function Hr(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ev=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),nv=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Jl(t){return nv.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var kr=null;function qr(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Da=null,Ra=null;function kf(t){var e=Sa(t);if(e&&(t=e.stateNode)){var n=t[re]||null;t:switch(t=e.stateNode,e.type){case"input":if(Br(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Ee(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var o=l[re]||null;if(!o)throw Error(r(90));Br(l,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&zf(l)}break t;case"textarea":Uf(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&Ma(t,!!n.multiple,e,!1)}}}var Yr=!1;function qf(t,e,n){if(Yr)return t(e,n);Yr=!0;try{var l=t(e);return l}finally{if(Yr=!1,(Da!==null||Ra!==null)&&(_s(),Da&&(e=Da,t=Ra,Ra=Da=null,kf(e),t)))for(e=0;e<t.length;e++)kf(t[e])}}function Si(t,e){var n=t.stateNode;if(n===null)return null;var l=n[re]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(r(231,e,typeof n));return n}var Ie=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Gr=!1;if(Ie)try{var Ti={};Object.defineProperty(Ti,"passive",{get:function(){Gr=!0}}),window.addEventListener("test",Ti,Ti),window.removeEventListener("test",Ti,Ti)}catch{Gr=!1}var Tn=null,Xr=null,Fl=null;function Yf(){if(Fl)return Fl;var t,e=Xr,n=e.length,l,o="value"in Tn?Tn.value:Tn.textContent,u=o.length;for(t=0;t<n&&e[t]===o[t];t++);var y=n-t;for(l=1;l<=y&&e[n-l]===o[u-l];l++);return Fl=o.slice(t,1<l?1-l:void 0)}function Wl(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Il(){return!0}function Gf(){return!1}function oe(t){function e(n,l,o,u,y){this._reactName=n,this._targetInst=o,this.type=l,this.nativeEvent=u,this.target=y,this.currentTarget=null;for(var v in t)t.hasOwnProperty(v)&&(n=t[v],this[v]=n?n(u):u[v]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Il:Gf,this.isPropagationStopped=Gf,this}return g(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Il)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Il)},persist:function(){},isPersistent:Il}),e}var Fn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ts=oe(Fn),Ai=g({},Fn,{view:0,detail:0}),av=oe(Ai),Kr,Zr,Ei,es=g({},Ai,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Pr,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Ei&&(Ei&&t.type==="mousemove"?(Kr=t.screenX-Ei.screenX,Zr=t.screenY-Ei.screenY):Zr=Kr=0,Ei=t),Kr)},movementY:function(t){return"movementY"in t?t.movementY:Zr}}),Xf=oe(es),iv=g({},es,{dataTransfer:0}),lv=oe(iv),sv=g({},Ai,{relatedTarget:0}),Qr=oe(sv),rv=g({},Fn,{animationName:0,elapsedTime:0,pseudoElement:0}),ov=oe(rv),uv=g({},Fn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),cv=oe(uv),fv=g({},Fn,{data:0}),Kf=oe(fv),dv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},hv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},mv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pv(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=mv[t])?!!e[t]:!1}function Pr(){return pv}var yv=g({},Ai,{key:function(t){if(t.key){var e=dv[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Wl(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?hv[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Pr,charCode:function(t){return t.type==="keypress"?Wl(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Wl(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),gv=oe(yv),vv=g({},es,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Zf=oe(vv),xv=g({},Ai,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Pr}),bv=oe(xv),Sv=g({},Fn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Tv=oe(Sv),Av=g({},es,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Ev=oe(Av),Mv=g({},Fn,{newState:0,oldState:0}),wv=oe(Mv),Dv=[9,13,27,32],$r=Ie&&"CompositionEvent"in window,Mi=null;Ie&&"documentMode"in document&&(Mi=document.documentMode);var Rv=Ie&&"TextEvent"in window&&!Mi,Qf=Ie&&(!$r||Mi&&8<Mi&&11>=Mi),Pf=" ",$f=!1;function Jf(t,e){switch(t){case"keyup":return Dv.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ff(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Ca=!1;function Cv(t,e){switch(t){case"compositionend":return Ff(e);case"keypress":return e.which!==32?null:($f=!0,Pf);case"textInput":return t=e.data,t===Pf&&$f?null:t;default:return null}}function Nv(t,e){if(Ca)return t==="compositionend"||!$r&&Jf(t,e)?(t=Yf(),Fl=Xr=Tn=null,Ca=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Qf&&e.locale!=="ko"?null:e.data;default:return null}}var jv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wf(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!jv[t.type]:e==="textarea"}function If(t,e,n,l){Da?Ra?Ra.push(l):Ra=[l]:Da=l,e=qs(e,"onChange"),0<e.length&&(n=new ts("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var wi=null,Di=null;function Ov(t){Vm(t,0)}function ns(t){var e=bi(t);if(zf(e))return t}function td(t,e){if(t==="change")return e}var ed=!1;if(Ie){var Jr;if(Ie){var Fr="oninput"in document;if(!Fr){var nd=document.createElement("div");nd.setAttribute("oninput","return;"),Fr=typeof nd.oninput=="function"}Jr=Fr}else Jr=!1;ed=Jr&&(!document.documentMode||9<document.documentMode)}function ad(){wi&&(wi.detachEvent("onpropertychange",id),Di=wi=null)}function id(t){if(t.propertyName==="value"&&ns(Di)){var e=[];If(e,Di,t,qr(t)),qf(Ov,e)}}function Vv(t,e,n){t==="focusin"?(ad(),wi=e,Di=n,wi.attachEvent("onpropertychange",id)):t==="focusout"&&ad()}function zv(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return ns(Di)}function _v(t,e){if(t==="click")return ns(e)}function Uv(t,e){if(t==="input"||t==="change")return ns(e)}function Bv(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var ye=typeof Object.is=="function"?Object.is:Bv;function Ri(t,e){if(ye(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var o=n[l];if(!wr.call(e,o)||!ye(t[o],e[o]))return!1}return!0}function ld(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function sd(t,e){var n=ld(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=ld(n)}}function rd(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?rd(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function od(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=$l(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=$l(t.document)}return e}function Wr(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Lv=Ie&&"documentMode"in document&&11>=document.documentMode,Na=null,Ir=null,Ci=null,to=!1;function ud(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;to||Na==null||Na!==$l(l)||(l=Na,"selectionStart"in l&&Wr(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Ci&&Ri(Ci,l)||(Ci=l,l=qs(Ir,"onSelect"),0<l.length&&(e=new ts("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=Na)))}function Wn(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var ja={animationend:Wn("Animation","AnimationEnd"),animationiteration:Wn("Animation","AnimationIteration"),animationstart:Wn("Animation","AnimationStart"),transitionrun:Wn("Transition","TransitionRun"),transitionstart:Wn("Transition","TransitionStart"),transitioncancel:Wn("Transition","TransitionCancel"),transitionend:Wn("Transition","TransitionEnd")},eo={},cd={};Ie&&(cd=document.createElement("div").style,"AnimationEvent"in window||(delete ja.animationend.animation,delete ja.animationiteration.animation,delete ja.animationstart.animation),"TransitionEvent"in window||delete ja.transitionend.transition);function In(t){if(eo[t])return eo[t];if(!ja[t])return t;var e=ja[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in cd)return eo[t]=e[n];return t}var fd=In("animationend"),dd=In("animationiteration"),hd=In("animationstart"),Hv=In("transitionrun"),kv=In("transitionstart"),qv=In("transitioncancel"),md=In("transitionend"),pd=new Map,no="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");no.push("scrollEnd");function _e(t,e){pd.set(t,e),Jn(e,[t])}var yd=new WeakMap;function Me(t,e){if(typeof t=="object"&&t!==null){var n=yd.get(t);return n!==void 0?n:(e={value:t,source:e,stack:Of(e)},yd.set(t,e),e)}return{value:t,source:e,stack:Of(e)}}var we=[],Oa=0,ao=0;function as(){for(var t=Oa,e=ao=Oa=0;e<t;){var n=we[e];we[e++]=null;var l=we[e];we[e++]=null;var o=we[e];we[e++]=null;var u=we[e];if(we[e++]=null,l!==null&&o!==null){var y=l.pending;y===null?o.next=o:(o.next=y.next,y.next=o),l.pending=o}u!==0&&gd(n,o,u)}}function is(t,e,n,l){we[Oa++]=t,we[Oa++]=e,we[Oa++]=n,we[Oa++]=l,ao|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function io(t,e,n,l){return is(t,e,n,l),ls(t)}function Va(t,e){return is(t,null,null,e),ls(t)}function gd(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var o=!1,u=t.return;u!==null;)u.childLanes|=n,l=u.alternate,l!==null&&(l.childLanes|=n),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(o=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,o&&e!==null&&(o=31-pe(n),t=u.hiddenUpdates,l=t[o],l===null?t[o]=[e]:l.push(e),e.lane=n|536870912),u):null}function ls(t){if(50<tl)throw tl=0,fu=null,Error(r(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var za={};function Yv(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ge(t,e,n,l){return new Yv(t,e,n,l)}function lo(t){return t=t.prototype,!(!t||!t.isReactComponent)}function tn(t,e){var n=t.alternate;return n===null?(n=ge(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function vd(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function ss(t,e,n,l,o,u){var y=0;if(l=t,typeof t=="function")lo(t)&&(y=1);else if(typeof t=="string")y=X1(t,n,W.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case rt:return t=ge(31,n,e,o),t.elementType=rt,t.lanes=u,t;case D:return ta(n.children,o,u,e);case z:y=8,o|=24;break;case B:return t=ge(12,n,e,o|2),t.elementType=B,t.lanes=u,t;case G:return t=ge(13,n,e,o),t.elementType=G,t.lanes=u,t;case at:return t=ge(19,n,e,o),t.elementType=at,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case V:case k:y=10;break t;case q:y=9;break t;case P:y=11;break t;case lt:y=14;break t;case J:y=16,l=null;break t}y=29,n=Error(r(130,t===null?"null":typeof t,"")),l=null}return e=ge(y,n,e,o),e.elementType=t,e.type=l,e.lanes=u,e}function ta(t,e,n,l){return t=ge(7,t,l,e),t.lanes=n,t}function so(t,e,n){return t=ge(6,t,null,e),t.lanes=n,t}function ro(t,e,n){return e=ge(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var _a=[],Ua=0,rs=null,os=0,De=[],Re=0,ea=null,en=1,nn="";function na(t,e){_a[Ua++]=os,_a[Ua++]=rs,rs=t,os=e}function xd(t,e,n){De[Re++]=en,De[Re++]=nn,De[Re++]=ea,ea=t;var l=en;t=nn;var o=32-pe(l)-1;l&=~(1<<o),n+=1;var u=32-pe(e)+o;if(30<u){var y=o-o%5;u=(l&(1<<y)-1).toString(32),l>>=y,o-=y,en=1<<32-pe(e)+o|n<<o|l,nn=u+t}else en=1<<u|n<<o|l,nn=t}function oo(t){t.return!==null&&(na(t,1),xd(t,1,0))}function uo(t){for(;t===rs;)rs=_a[--Ua],_a[Ua]=null,os=_a[--Ua],_a[Ua]=null;for(;t===ea;)ea=De[--Re],De[Re]=null,nn=De[--Re],De[Re]=null,en=De[--Re],De[Re]=null}var le=null,Vt=null,gt=!1,aa=null,qe=!1,co=Error(r(519));function ia(t){var e=Error(r(418,""));throw Oi(Me(e,t)),co}function bd(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[ee]=t,e[re]=l,n){case"dialog":ft("cancel",e),ft("close",e);break;case"iframe":case"object":case"embed":ft("load",e);break;case"video":case"audio":for(n=0;n<nl.length;n++)ft(nl[n],e);break;case"source":ft("error",e);break;case"img":case"image":case"link":ft("error",e),ft("load",e);break;case"details":ft("toggle",e);break;case"input":ft("invalid",e),_f(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),Pl(e);break;case"select":ft("invalid",e);break;case"textarea":ft("invalid",e),Bf(e,l.value,l.defaultValue,l.children),Pl(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||Bm(e.textContent,n)?(l.popover!=null&&(ft("beforetoggle",e),ft("toggle",e)),l.onScroll!=null&&ft("scroll",e),l.onScrollEnd!=null&&ft("scrollend",e),l.onClick!=null&&(e.onclick=Ys),e=!0):e=!1,e||ia(t)}function Sd(t){for(le=t.return;le;)switch(le.tag){case 5:case 13:qe=!1;return;case 27:case 3:qe=!0;return;default:le=le.return}}function Ni(t){if(t!==le)return!1;if(!gt)return Sd(t),gt=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||Du(t.type,t.memoizedProps)),n=!n),n&&Vt&&ia(t),Sd(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(r(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){Vt=Be(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}Vt=null}}else e===27?(e=Vt,Ln(t.type)?(t=ju,ju=null,Vt=t):Vt=e):Vt=le?Be(t.stateNode.nextSibling):null;return!0}function ji(){Vt=le=null,gt=!1}function Td(){var t=aa;return t!==null&&(fe===null?fe=t:fe.push.apply(fe,t),aa=null),t}function Oi(t){aa===null?aa=[t]:aa.push(t)}var fo=Y(null),la=null,an=null;function An(t,e,n){K(fo,e._currentValue),e._currentValue=n}function ln(t){t._currentValue=fo.current,Z(fo)}function ho(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function mo(t,e,n,l){var o=t.child;for(o!==null&&(o.return=t);o!==null;){var u=o.dependencies;if(u!==null){var y=o.child;u=u.firstContext;t:for(;u!==null;){var v=u;u=o;for(var T=0;T<e.length;T++)if(v.context===e[T]){u.lanes|=n,v=u.alternate,v!==null&&(v.lanes|=n),ho(u.return,n,t),l||(y=null);break t}u=v.next}}else if(o.tag===18){if(y=o.return,y===null)throw Error(r(341));y.lanes|=n,u=y.alternate,u!==null&&(u.lanes|=n),ho(y,n,t),y=null}else y=o.child;if(y!==null)y.return=o;else for(y=o;y!==null;){if(y===t){y=null;break}if(o=y.sibling,o!==null){o.return=y.return,y=o;break}y=y.return}o=y}}function Vi(t,e,n,l){t=null;for(var o=e,u=!1;o!==null;){if(!u){if((o.flags&524288)!==0)u=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var y=o.alternate;if(y===null)throw Error(r(387));if(y=y.memoizedProps,y!==null){var v=o.type;ye(o.pendingProps.value,y.value)||(t!==null?t.push(v):t=[v])}}else if(o===he.current){if(y=o.alternate,y===null)throw Error(r(387));y.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(t!==null?t.push(ol):t=[ol])}o=o.return}t!==null&&mo(e,t,n,l),e.flags|=262144}function us(t){for(t=t.firstContext;t!==null;){if(!ye(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function sa(t){la=t,an=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ne(t){return Ad(la,t)}function cs(t,e){return la===null&&sa(t),Ad(t,e)}function Ad(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},an===null){if(t===null)throw Error(r(308));an=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else an=an.next=e;return n}var Gv=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},Xv=a.unstable_scheduleCallback,Kv=a.unstable_NormalPriority,Xt={$$typeof:k,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function po(){return{controller:new Gv,data:new Map,refCount:0}}function zi(t){t.refCount--,t.refCount===0&&Xv(Kv,function(){t.controller.abort()})}var _i=null,yo=0,Ba=0,La=null;function Zv(t,e){if(_i===null){var n=_i=[];yo=0,Ba=vu(),La={status:"pending",value:void 0,then:function(l){n.push(l)}}}return yo++,e.then(Ed,Ed),e}function Ed(){if(--yo===0&&_i!==null){La!==null&&(La.status="fulfilled");var t=_i;_i=null,Ba=0,La=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Qv(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(o){n.push(o)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var o=0;o<n.length;o++)(0,n[o])(e)},function(o){for(l.status="rejected",l.reason=o,o=0;o<n.length;o++)(0,n[o])(void 0)}),l}var Md=U.S;U.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Zv(t,e),Md!==null&&Md(t,e)};var ra=Y(null);function go(){var t=ra.current;return t!==null?t:Mt.pooledCache}function fs(t,e){e===null?K(ra,ra.current):K(ra,e.pool)}function wd(){var t=go();return t===null?null:{parent:Xt._currentValue,pool:t}}var Ui=Error(r(460)),Dd=Error(r(474)),ds=Error(r(542)),vo={then:function(){}};function Rd(t){return t=t.status,t==="fulfilled"||t==="rejected"}function hs(){}function Cd(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(hs,hs),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,jd(t),t;default:if(typeof e.status=="string")e.then(hs,hs);else{if(t=Mt,t!==null&&100<t.shellSuspendCounter)throw Error(r(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var o=e;o.status="fulfilled",o.value=l}},function(l){if(e.status==="pending"){var o=e;o.status="rejected",o.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,jd(t),t}throw Bi=e,Ui}}var Bi=null;function Nd(){if(Bi===null)throw Error(r(459));var t=Bi;return Bi=null,t}function jd(t){if(t===Ui||t===ds)throw Error(r(483))}var En=!1;function xo(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function bo(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Mn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function wn(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(vt&2)!==0){var o=l.pending;return o===null?e.next=e:(e.next=o.next,o.next=e),l.pending=e,e=ls(t),gd(t,null,n),e}return is(t,l,e,n),ls(t)}function Li(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,Ef(t,n)}}function So(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var o=null,u=null;if(n=n.firstBaseUpdate,n!==null){do{var y={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};u===null?o=u=y:u=u.next=y,n=n.next}while(n!==null);u===null?o=u=e:u=u.next=e}else o=u=e;n={baseState:l.baseState,firstBaseUpdate:o,lastBaseUpdate:u,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var To=!1;function Hi(){if(To){var t=La;if(t!==null)throw t}}function ki(t,e,n,l){To=!1;var o=t.updateQueue;En=!1;var u=o.firstBaseUpdate,y=o.lastBaseUpdate,v=o.shared.pending;if(v!==null){o.shared.pending=null;var T=v,N=T.next;T.next=null,y===null?u=N:y.next=N,y=T;var _=t.alternate;_!==null&&(_=_.updateQueue,v=_.lastBaseUpdate,v!==y&&(v===null?_.firstBaseUpdate=N:v.next=N,_.lastBaseUpdate=T))}if(u!==null){var H=o.baseState;y=0,_=N=T=null,v=u;do{var j=v.lane&-536870913,O=j!==v.lane;if(O?(ht&j)===j:(l&j)===j){j!==0&&j===Ba&&(To=!0),_!==null&&(_=_.next={lane:0,tag:v.tag,payload:v.payload,callback:null,next:null});t:{var et=t,I=v;j=e;var At=n;switch(I.tag){case 1:if(et=I.payload,typeof et=="function"){H=et.call(At,H,j);break t}H=et;break t;case 3:et.flags=et.flags&-65537|128;case 0:if(et=I.payload,j=typeof et=="function"?et.call(At,H,j):et,j==null)break t;H=g({},H,j);break t;case 2:En=!0}}j=v.callback,j!==null&&(t.flags|=64,O&&(t.flags|=8192),O=o.callbacks,O===null?o.callbacks=[j]:O.push(j))}else O={lane:j,tag:v.tag,payload:v.payload,callback:v.callback,next:null},_===null?(N=_=O,T=H):_=_.next=O,y|=j;if(v=v.next,v===null){if(v=o.shared.pending,v===null)break;O=v,v=O.next,O.next=null,o.lastBaseUpdate=O,o.shared.pending=null}}while(!0);_===null&&(T=H),o.baseState=T,o.firstBaseUpdate=N,o.lastBaseUpdate=_,u===null&&(o.shared.lanes=0),zn|=y,t.lanes=y,t.memoizedState=H}}function Od(t,e){if(typeof t!="function")throw Error(r(191,t));t.call(e)}function Vd(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)Od(n[t],e)}var Ha=Y(null),ms=Y(0);function zd(t,e){t=dn,K(ms,t),K(Ha,e),dn=t|e.baseLanes}function Ao(){K(ms,dn),K(Ha,Ha.current)}function Eo(){dn=ms.current,Z(Ha),Z(ms)}var Dn=0,ot=null,St=null,Ht=null,ps=!1,ka=!1,oa=!1,ys=0,qi=0,qa=null,Pv=0;function Ut(){throw Error(r(321))}function Mo(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!ye(t[n],e[n]))return!1;return!0}function wo(t,e,n,l,o,u){return Dn=u,ot=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,U.H=t===null||t.memoizedState===null?gh:vh,oa=!1,u=n(l,o),oa=!1,ka&&(u=Ud(e,n,l,o)),_d(t),u}function _d(t){U.H=Ts;var e=St!==null&&St.next!==null;if(Dn=0,Ht=St=ot=null,ps=!1,qi=0,qa=null,e)throw Error(r(300));t===null||Qt||(t=t.dependencies,t!==null&&us(t)&&(Qt=!0))}function Ud(t,e,n,l){ot=t;var o=0;do{if(ka&&(qa=null),qi=0,ka=!1,25<=o)throw Error(r(301));if(o+=1,Ht=St=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}U.H=e1,u=e(n,l)}while(ka);return u}function $v(){var t=U.H,e=t.useState()[0];return e=typeof e.then=="function"?Yi(e):e,t=t.useState()[0],(St!==null?St.memoizedState:null)!==t&&(ot.flags|=1024),e}function Do(){var t=ys!==0;return ys=0,t}function Ro(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function Co(t){if(ps){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}ps=!1}Dn=0,Ht=St=ot=null,ka=!1,qi=ys=0,qa=null}function ue(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ht===null?ot.memoizedState=Ht=t:Ht=Ht.next=t,Ht}function kt(){if(St===null){var t=ot.alternate;t=t!==null?t.memoizedState:null}else t=St.next;var e=Ht===null?ot.memoizedState:Ht.next;if(e!==null)Ht=e,St=t;else{if(t===null)throw ot.alternate===null?Error(r(467)):Error(r(310));St=t,t={memoizedState:St.memoizedState,baseState:St.baseState,baseQueue:St.baseQueue,queue:St.queue,next:null},Ht===null?ot.memoizedState=Ht=t:Ht=Ht.next=t}return Ht}function No(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Yi(t){var e=qi;return qi+=1,qa===null&&(qa=[]),t=Cd(qa,t,e),e=ot,(Ht===null?e.memoizedState:Ht.next)===null&&(e=e.alternate,U.H=e===null||e.memoizedState===null?gh:vh),t}function gs(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Yi(t);if(t.$$typeof===k)return ne(t)}throw Error(r(438,String(t)))}function jo(t){var e=null,n=ot.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=ot.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(o){return o.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=No(),ot.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=bt;return e.index++,n}function sn(t,e){return typeof e=="function"?e(t):e}function vs(t){var e=kt();return Oo(e,St,t)}function Oo(t,e,n){var l=t.queue;if(l===null)throw Error(r(311));l.lastRenderedReducer=n;var o=t.baseQueue,u=l.pending;if(u!==null){if(o!==null){var y=o.next;o.next=u.next,u.next=y}e.baseQueue=o=u,l.pending=null}if(u=t.baseState,o===null)t.memoizedState=u;else{e=o.next;var v=y=null,T=null,N=e,_=!1;do{var H=N.lane&-536870913;if(H!==N.lane?(ht&H)===H:(Dn&H)===H){var j=N.revertLane;if(j===0)T!==null&&(T=T.next={lane:0,revertLane:0,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null}),H===Ba&&(_=!0);else if((Dn&j)===j){N=N.next,j===Ba&&(_=!0);continue}else H={lane:0,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},T===null?(v=T=H,y=u):T=T.next=H,ot.lanes|=j,zn|=j;H=N.action,oa&&n(u,H),u=N.hasEagerState?N.eagerState:n(u,H)}else j={lane:H,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},T===null?(v=T=j,y=u):T=T.next=j,ot.lanes|=H,zn|=H;N=N.next}while(N!==null&&N!==e);if(T===null?y=u:T.next=v,!ye(u,t.memoizedState)&&(Qt=!0,_&&(n=La,n!==null)))throw n;t.memoizedState=u,t.baseState=y,t.baseQueue=T,l.lastRenderedState=u}return o===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function Vo(t){var e=kt(),n=e.queue;if(n===null)throw Error(r(311));n.lastRenderedReducer=t;var l=n.dispatch,o=n.pending,u=e.memoizedState;if(o!==null){n.pending=null;var y=o=o.next;do u=t(u,y.action),y=y.next;while(y!==o);ye(u,e.memoizedState)||(Qt=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),n.lastRenderedState=u}return[u,l]}function Bd(t,e,n){var l=ot,o=kt(),u=gt;if(u){if(n===void 0)throw Error(r(407));n=n()}else n=e();var y=!ye((St||o).memoizedState,n);y&&(o.memoizedState=n,Qt=!0),o=o.queue;var v=kd.bind(null,l,o,t);if(Gi(2048,8,v,[t]),o.getSnapshot!==e||y||Ht!==null&&Ht.memoizedState.tag&1){if(l.flags|=2048,Ya(9,xs(),Hd.bind(null,l,o,n,e),null),Mt===null)throw Error(r(349));u||(Dn&124)!==0||Ld(l,e,n)}return n}function Ld(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=ot.updateQueue,e===null?(e=No(),ot.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Hd(t,e,n,l){e.value=n,e.getSnapshot=l,qd(e)&&Yd(t)}function kd(t,e,n){return n(function(){qd(e)&&Yd(t)})}function qd(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!ye(t,n)}catch{return!0}}function Yd(t){var e=Va(t,2);e!==null&&Te(e,t,2)}function zo(t){var e=ue();if(typeof t=="function"){var n=t;if(t=n(),oa){bn(!0);try{n()}finally{bn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:sn,lastRenderedState:t},e}function Gd(t,e,n,l){return t.baseState=n,Oo(t,St,typeof l=="function"?l:sn)}function Jv(t,e,n,l,o){if(Ss(t))throw Error(r(485));if(t=e.action,t!==null){var u={payload:o,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){u.listeners.push(y)}};U.T!==null?n(!0):u.isTransition=!1,l(u),n=e.pending,n===null?(u.next=e.pending=u,Xd(e,u)):(u.next=n.next,e.pending=n.next=u)}}function Xd(t,e){var n=e.action,l=e.payload,o=t.state;if(e.isTransition){var u=U.T,y={};U.T=y;try{var v=n(o,l),T=U.S;T!==null&&T(y,v),Kd(t,e,v)}catch(N){_o(t,e,N)}finally{U.T=u}}else try{u=n(o,l),Kd(t,e,u)}catch(N){_o(t,e,N)}}function Kd(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Zd(t,e,l)},function(l){return _o(t,e,l)}):Zd(t,e,n)}function Zd(t,e,n){e.status="fulfilled",e.value=n,Qd(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,Xd(t,n)))}function _o(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,Qd(e),e=e.next;while(e!==l)}t.action=null}function Qd(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Pd(t,e){return e}function $d(t,e){if(gt){var n=Mt.formState;if(n!==null){t:{var l=ot;if(gt){if(Vt){e:{for(var o=Vt,u=qe;o.nodeType!==8;){if(!u){o=null;break e}if(o=Be(o.nextSibling),o===null){o=null;break e}}u=o.data,o=u==="F!"||u==="F"?o:null}if(o){Vt=Be(o.nextSibling),l=o.data==="F!";break t}}ia(l)}l=!1}l&&(e=n[0])}}return n=ue(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Pd,lastRenderedState:e},n.queue=l,n=mh.bind(null,ot,l),l.dispatch=n,l=zo(!1),u=ko.bind(null,ot,!1,l.queue),l=ue(),o={state:e,dispatch:null,action:t,pending:null},l.queue=o,n=Jv.bind(null,ot,o,u,n),o.dispatch=n,l.memoizedState=t,[e,n,!1]}function Jd(t){var e=kt();return Fd(e,St,t)}function Fd(t,e,n){if(e=Oo(t,e,Pd)[0],t=vs(sn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=Yi(e)}catch(y){throw y===Ui?ds:y}else l=e;e=kt();var o=e.queue,u=o.dispatch;return n!==e.memoizedState&&(ot.flags|=2048,Ya(9,xs(),Fv.bind(null,o,n),null)),[l,u,t]}function Fv(t,e){t.action=e}function Wd(t){var e=kt(),n=St;if(n!==null)return Fd(e,n,t);kt(),e=e.memoizedState,n=kt();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function Ya(t,e,n,l){return t={tag:t,create:n,deps:l,inst:e,next:null},e=ot.updateQueue,e===null&&(e=No(),ot.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function xs(){return{destroy:void 0,resource:void 0}}function Id(){return kt().memoizedState}function bs(t,e,n,l){var o=ue();l=l===void 0?null:l,ot.flags|=t,o.memoizedState=Ya(1|e,xs(),n,l)}function Gi(t,e,n,l){var o=kt();l=l===void 0?null:l;var u=o.memoizedState.inst;St!==null&&l!==null&&Mo(l,St.memoizedState.deps)?o.memoizedState=Ya(e,u,n,l):(ot.flags|=t,o.memoizedState=Ya(1|e,u,n,l))}function th(t,e){bs(8390656,8,t,e)}function eh(t,e){Gi(2048,8,t,e)}function nh(t,e){return Gi(4,2,t,e)}function ah(t,e){return Gi(4,4,t,e)}function ih(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function lh(t,e,n){n=n!=null?n.concat([t]):null,Gi(4,4,ih.bind(null,e,t),n)}function Uo(){}function sh(t,e){var n=kt();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&Mo(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function rh(t,e){var n=kt();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&Mo(e,l[1]))return l[0];if(l=t(),oa){bn(!0);try{t()}finally{bn(!1)}}return n.memoizedState=[l,e],l}function Bo(t,e,n){return n===void 0||(Dn&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=cm(),ot.lanes|=t,zn|=t,n)}function oh(t,e,n,l){return ye(n,e)?n:Ha.current!==null?(t=Bo(t,n,l),ye(t,e)||(Qt=!0),t):(Dn&42)===0?(Qt=!0,t.memoizedState=n):(t=cm(),ot.lanes|=t,zn|=t,e)}function uh(t,e,n,l,o){var u=X.p;X.p=u!==0&&8>u?u:8;var y=U.T,v={};U.T=v,ko(t,!1,e,n);try{var T=o(),N=U.S;if(N!==null&&N(v,T),T!==null&&typeof T=="object"&&typeof T.then=="function"){var _=Qv(T,l);Xi(t,e,_,Se(t))}else Xi(t,e,l,Se(t))}catch(H){Xi(t,e,{then:function(){},status:"rejected",reason:H},Se())}finally{X.p=u,U.T=y}}function Wv(){}function Lo(t,e,n,l){if(t.tag!==5)throw Error(r(476));var o=ch(t).queue;uh(t,o,e,$,n===null?Wv:function(){return fh(t),n(l)})}function ch(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:$,baseState:$,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:sn,lastRenderedState:$},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:sn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function fh(t){var e=ch(t).next.queue;Xi(t,e,{},Se())}function Ho(){return ne(ol)}function dh(){return kt().memoizedState}function hh(){return kt().memoizedState}function Iv(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=Se();t=Mn(n);var l=wn(e,t,n);l!==null&&(Te(l,e,n),Li(l,e,n)),e={cache:po()},t.payload=e;return}e=e.return}}function t1(t,e,n){var l=Se();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Ss(t)?ph(e,n):(n=io(t,e,n,l),n!==null&&(Te(n,t,l),yh(n,e,l)))}function mh(t,e,n){var l=Se();Xi(t,e,n,l)}function Xi(t,e,n,l){var o={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ss(t))ph(e,o);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var y=e.lastRenderedState,v=u(y,n);if(o.hasEagerState=!0,o.eagerState=v,ye(v,y))return is(t,e,o,0),Mt===null&&as(),!1}catch{}finally{}if(n=io(t,e,o,l),n!==null)return Te(n,t,l),yh(n,e,l),!0}return!1}function ko(t,e,n,l){if(l={lane:2,revertLane:vu(),action:l,hasEagerState:!1,eagerState:null,next:null},Ss(t)){if(e)throw Error(r(479))}else e=io(t,n,l,2),e!==null&&Te(e,t,2)}function Ss(t){var e=t.alternate;return t===ot||e!==null&&e===ot}function ph(t,e){ka=ps=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function yh(t,e,n){if((n&4194048)!==0){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,Ef(t,n)}}var Ts={readContext:ne,use:gs,useCallback:Ut,useContext:Ut,useEffect:Ut,useImperativeHandle:Ut,useLayoutEffect:Ut,useInsertionEffect:Ut,useMemo:Ut,useReducer:Ut,useRef:Ut,useState:Ut,useDebugValue:Ut,useDeferredValue:Ut,useTransition:Ut,useSyncExternalStore:Ut,useId:Ut,useHostTransitionStatus:Ut,useFormState:Ut,useActionState:Ut,useOptimistic:Ut,useMemoCache:Ut,useCacheRefresh:Ut},gh={readContext:ne,use:gs,useCallback:function(t,e){return ue().memoizedState=[t,e===void 0?null:e],t},useContext:ne,useEffect:th,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,bs(4194308,4,ih.bind(null,e,t),n)},useLayoutEffect:function(t,e){return bs(4194308,4,t,e)},useInsertionEffect:function(t,e){bs(4,2,t,e)},useMemo:function(t,e){var n=ue();e=e===void 0?null:e;var l=t();if(oa){bn(!0);try{t()}finally{bn(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=ue();if(n!==void 0){var o=n(e);if(oa){bn(!0);try{n(e)}finally{bn(!1)}}}else o=e;return l.memoizedState=l.baseState=o,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:o},l.queue=t,t=t.dispatch=t1.bind(null,ot,t),[l.memoizedState,t]},useRef:function(t){var e=ue();return t={current:t},e.memoizedState=t},useState:function(t){t=zo(t);var e=t.queue,n=mh.bind(null,ot,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:Uo,useDeferredValue:function(t,e){var n=ue();return Bo(n,t,e)},useTransition:function(){var t=zo(!1);return t=uh.bind(null,ot,t.queue,!0,!1),ue().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=ot,o=ue();if(gt){if(n===void 0)throw Error(r(407));n=n()}else{if(n=e(),Mt===null)throw Error(r(349));(ht&124)!==0||Ld(l,e,n)}o.memoizedState=n;var u={value:n,getSnapshot:e};return o.queue=u,th(kd.bind(null,l,u,t),[t]),l.flags|=2048,Ya(9,xs(),Hd.bind(null,l,u,n,e),null),n},useId:function(){var t=ue(),e=Mt.identifierPrefix;if(gt){var n=nn,l=en;n=(l&~(1<<32-pe(l)-1)).toString(32)+n,e="«"+e+"R"+n,n=ys++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=Pv++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Ho,useFormState:$d,useActionState:$d,useOptimistic:function(t){var e=ue();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=ko.bind(null,ot,!0,n),n.dispatch=e,[t,e]},useMemoCache:jo,useCacheRefresh:function(){return ue().memoizedState=Iv.bind(null,ot)}},vh={readContext:ne,use:gs,useCallback:sh,useContext:ne,useEffect:eh,useImperativeHandle:lh,useInsertionEffect:nh,useLayoutEffect:ah,useMemo:rh,useReducer:vs,useRef:Id,useState:function(){return vs(sn)},useDebugValue:Uo,useDeferredValue:function(t,e){var n=kt();return oh(n,St.memoizedState,t,e)},useTransition:function(){var t=vs(sn)[0],e=kt().memoizedState;return[typeof t=="boolean"?t:Yi(t),e]},useSyncExternalStore:Bd,useId:dh,useHostTransitionStatus:Ho,useFormState:Jd,useActionState:Jd,useOptimistic:function(t,e){var n=kt();return Gd(n,St,t,e)},useMemoCache:jo,useCacheRefresh:hh},e1={readContext:ne,use:gs,useCallback:sh,useContext:ne,useEffect:eh,useImperativeHandle:lh,useInsertionEffect:nh,useLayoutEffect:ah,useMemo:rh,useReducer:Vo,useRef:Id,useState:function(){return Vo(sn)},useDebugValue:Uo,useDeferredValue:function(t,e){var n=kt();return St===null?Bo(n,t,e):oh(n,St.memoizedState,t,e)},useTransition:function(){var t=Vo(sn)[0],e=kt().memoizedState;return[typeof t=="boolean"?t:Yi(t),e]},useSyncExternalStore:Bd,useId:dh,useHostTransitionStatus:Ho,useFormState:Wd,useActionState:Wd,useOptimistic:function(t,e){var n=kt();return St!==null?Gd(n,St,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:jo,useCacheRefresh:hh},Ga=null,Ki=0;function As(t){var e=Ki;return Ki+=1,Ga===null&&(Ga=[]),Cd(Ga,t,e)}function Zi(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Es(t,e){throw e.$$typeof===x?Error(r(525)):(t=Object.prototype.toString.call(e),Error(r(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function xh(t){var e=t._init;return e(t._payload)}function bh(t){function e(w,E){if(t){var R=w.deletions;R===null?(w.deletions=[E],w.flags|=16):R.push(E)}}function n(w,E){if(!t)return null;for(;E!==null;)e(w,E),E=E.sibling;return null}function l(w){for(var E=new Map;w!==null;)w.key!==null?E.set(w.key,w):E.set(w.index,w),w=w.sibling;return E}function o(w,E){return w=tn(w,E),w.index=0,w.sibling=null,w}function u(w,E,R){return w.index=R,t?(R=w.alternate,R!==null?(R=R.index,R<E?(w.flags|=67108866,E):R):(w.flags|=67108866,E)):(w.flags|=1048576,E)}function y(w){return t&&w.alternate===null&&(w.flags|=67108866),w}function v(w,E,R,L){return E===null||E.tag!==6?(E=so(R,w.mode,L),E.return=w,E):(E=o(E,R),E.return=w,E)}function T(w,E,R,L){var Q=R.type;return Q===D?_(w,E,R.props.children,L,R.key):E!==null&&(E.elementType===Q||typeof Q=="object"&&Q!==null&&Q.$$typeof===J&&xh(Q)===E.type)?(E=o(E,R.props),Zi(E,R),E.return=w,E):(E=ss(R.type,R.key,R.props,null,w.mode,L),Zi(E,R),E.return=w,E)}function N(w,E,R,L){return E===null||E.tag!==4||E.stateNode.containerInfo!==R.containerInfo||E.stateNode.implementation!==R.implementation?(E=ro(R,w.mode,L),E.return=w,E):(E=o(E,R.children||[]),E.return=w,E)}function _(w,E,R,L,Q){return E===null||E.tag!==7?(E=ta(R,w.mode,L,Q),E.return=w,E):(E=o(E,R),E.return=w,E)}function H(w,E,R){if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return E=so(""+E,w.mode,R),E.return=w,E;if(typeof E=="object"&&E!==null){switch(E.$$typeof){case b:return R=ss(E.type,E.key,E.props,null,w.mode,R),Zi(R,E),R.return=w,R;case M:return E=ro(E,w.mode,R),E.return=w,E;case J:var L=E._init;return E=L(E._payload),H(w,E,R)}if(Jt(E)||Gt(E))return E=ta(E,w.mode,R,null),E.return=w,E;if(typeof E.then=="function")return H(w,As(E),R);if(E.$$typeof===k)return H(w,cs(w,E),R);Es(w,E)}return null}function j(w,E,R,L){var Q=E!==null?E.key:null;if(typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint")return Q!==null?null:v(w,E,""+R,L);if(typeof R=="object"&&R!==null){switch(R.$$typeof){case b:return R.key===Q?T(w,E,R,L):null;case M:return R.key===Q?N(w,E,R,L):null;case J:return Q=R._init,R=Q(R._payload),j(w,E,R,L)}if(Jt(R)||Gt(R))return Q!==null?null:_(w,E,R,L,null);if(typeof R.then=="function")return j(w,E,As(R),L);if(R.$$typeof===k)return j(w,E,cs(w,R),L);Es(w,R)}return null}function O(w,E,R,L,Q){if(typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint")return w=w.get(R)||null,v(E,w,""+L,Q);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case b:return w=w.get(L.key===null?R:L.key)||null,T(E,w,L,Q);case M:return w=w.get(L.key===null?R:L.key)||null,N(E,w,L,Q);case J:var ut=L._init;return L=ut(L._payload),O(w,E,R,L,Q)}if(Jt(L)||Gt(L))return w=w.get(R)||null,_(E,w,L,Q,null);if(typeof L.then=="function")return O(w,E,R,As(L),Q);if(L.$$typeof===k)return O(w,E,R,cs(E,L),Q);Es(E,L)}return null}function et(w,E,R,L){for(var Q=null,ut=null,F=E,tt=E=0,$t=null;F!==null&&tt<R.length;tt++){F.index>tt?($t=F,F=null):$t=F.sibling;var pt=j(w,F,R[tt],L);if(pt===null){F===null&&(F=$t);break}t&&F&&pt.alternate===null&&e(w,F),E=u(pt,E,tt),ut===null?Q=pt:ut.sibling=pt,ut=pt,F=$t}if(tt===R.length)return n(w,F),gt&&na(w,tt),Q;if(F===null){for(;tt<R.length;tt++)F=H(w,R[tt],L),F!==null&&(E=u(F,E,tt),ut===null?Q=F:ut.sibling=F,ut=F);return gt&&na(w,tt),Q}for(F=l(F);tt<R.length;tt++)$t=O(F,w,tt,R[tt],L),$t!==null&&(t&&$t.alternate!==null&&F.delete($t.key===null?tt:$t.key),E=u($t,E,tt),ut===null?Q=$t:ut.sibling=$t,ut=$t);return t&&F.forEach(function(Gn){return e(w,Gn)}),gt&&na(w,tt),Q}function I(w,E,R,L){if(R==null)throw Error(r(151));for(var Q=null,ut=null,F=E,tt=E=0,$t=null,pt=R.next();F!==null&&!pt.done;tt++,pt=R.next()){F.index>tt?($t=F,F=null):$t=F.sibling;var Gn=j(w,F,pt.value,L);if(Gn===null){F===null&&(F=$t);break}t&&F&&Gn.alternate===null&&e(w,F),E=u(Gn,E,tt),ut===null?Q=Gn:ut.sibling=Gn,ut=Gn,F=$t}if(pt.done)return n(w,F),gt&&na(w,tt),Q;if(F===null){for(;!pt.done;tt++,pt=R.next())pt=H(w,pt.value,L),pt!==null&&(E=u(pt,E,tt),ut===null?Q=pt:ut.sibling=pt,ut=pt);return gt&&na(w,tt),Q}for(F=l(F);!pt.done;tt++,pt=R.next())pt=O(F,w,tt,pt.value,L),pt!==null&&(t&&pt.alternate!==null&&F.delete(pt.key===null?tt:pt.key),E=u(pt,E,tt),ut===null?Q=pt:ut.sibling=pt,ut=pt);return t&&F.forEach(function(nx){return e(w,nx)}),gt&&na(w,tt),Q}function At(w,E,R,L){if(typeof R=="object"&&R!==null&&R.type===D&&R.key===null&&(R=R.props.children),typeof R=="object"&&R!==null){switch(R.$$typeof){case b:t:{for(var Q=R.key;E!==null;){if(E.key===Q){if(Q=R.type,Q===D){if(E.tag===7){n(w,E.sibling),L=o(E,R.props.children),L.return=w,w=L;break t}}else if(E.elementType===Q||typeof Q=="object"&&Q!==null&&Q.$$typeof===J&&xh(Q)===E.type){n(w,E.sibling),L=o(E,R.props),Zi(L,R),L.return=w,w=L;break t}n(w,E);break}else e(w,E);E=E.sibling}R.type===D?(L=ta(R.props.children,w.mode,L,R.key),L.return=w,w=L):(L=ss(R.type,R.key,R.props,null,w.mode,L),Zi(L,R),L.return=w,w=L)}return y(w);case M:t:{for(Q=R.key;E!==null;){if(E.key===Q)if(E.tag===4&&E.stateNode.containerInfo===R.containerInfo&&E.stateNode.implementation===R.implementation){n(w,E.sibling),L=o(E,R.children||[]),L.return=w,w=L;break t}else{n(w,E);break}else e(w,E);E=E.sibling}L=ro(R,w.mode,L),L.return=w,w=L}return y(w);case J:return Q=R._init,R=Q(R._payload),At(w,E,R,L)}if(Jt(R))return et(w,E,R,L);if(Gt(R)){if(Q=Gt(R),typeof Q!="function")throw Error(r(150));return R=Q.call(R),I(w,E,R,L)}if(typeof R.then=="function")return At(w,E,As(R),L);if(R.$$typeof===k)return At(w,E,cs(w,R),L);Es(w,R)}return typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint"?(R=""+R,E!==null&&E.tag===6?(n(w,E.sibling),L=o(E,R),L.return=w,w=L):(n(w,E),L=so(R,w.mode,L),L.return=w,w=L),y(w)):n(w,E)}return function(w,E,R,L){try{Ki=0;var Q=At(w,E,R,L);return Ga=null,Q}catch(F){if(F===Ui||F===ds)throw F;var ut=ge(29,F,null,w.mode);return ut.lanes=L,ut.return=w,ut}finally{}}}var Xa=bh(!0),Sh=bh(!1),Ce=Y(null),Ye=null;function Rn(t){var e=t.alternate;K(Kt,Kt.current&1),K(Ce,t),Ye===null&&(e===null||Ha.current!==null||e.memoizedState!==null)&&(Ye=t)}function Th(t){if(t.tag===22){if(K(Kt,Kt.current),K(Ce,t),Ye===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ye=t)}}else Cn()}function Cn(){K(Kt,Kt.current),K(Ce,Ce.current)}function rn(t){Z(Ce),Ye===t&&(Ye=null),Z(Kt)}var Kt=Y(0);function Ms(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Nu(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function qo(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:g({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Yo={enqueueSetState:function(t,e,n){t=t._reactInternals;var l=Se(),o=Mn(l);o.payload=e,n!=null&&(o.callback=n),e=wn(t,o,l),e!==null&&(Te(e,t,l),Li(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=Se(),o=Mn(l);o.tag=1,o.payload=e,n!=null&&(o.callback=n),e=wn(t,o,l),e!==null&&(Te(e,t,l),Li(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=Se(),l=Mn(n);l.tag=2,e!=null&&(l.callback=e),e=wn(t,l,n),e!==null&&(Te(e,t,n),Li(e,t,n))}};function Ah(t,e,n,l,o,u,y){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,u,y):e.prototype&&e.prototype.isPureReactComponent?!Ri(n,l)||!Ri(o,u):!0}function Eh(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&Yo.enqueueReplaceState(e,e.state,null)}function ua(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=g({},n));for(var o in t)n[o]===void 0&&(n[o]=t[o])}return n}var ws=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Mh(t){ws(t)}function wh(t){console.error(t)}function Dh(t){ws(t)}function Ds(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Rh(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function Go(t,e,n){return n=Mn(n),n.tag=3,n.payload={element:null},n.callback=function(){Ds(t,e)},n}function Ch(t){return t=Mn(t),t.tag=3,t}function Nh(t,e,n,l){var o=n.type.getDerivedStateFromError;if(typeof o=="function"){var u=l.value;t.payload=function(){return o(u)},t.callback=function(){Rh(e,n,l)}}var y=n.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(t.callback=function(){Rh(e,n,l),typeof o!="function"&&(_n===null?_n=new Set([this]):_n.add(this));var v=l.stack;this.componentDidCatch(l.value,{componentStack:v!==null?v:""})})}function n1(t,e,n,l,o){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&Vi(e,n,o,!0),n=Ce.current,n!==null){switch(n.tag){case 13:return Ye===null?hu():n.alternate===null&&zt===0&&(zt=3),n.flags&=-257,n.flags|=65536,n.lanes=o,l===vo?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),pu(t,l,o)),!1;case 22:return n.flags|=65536,l===vo?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),pu(t,l,o)),!1}throw Error(r(435,n.tag))}return pu(t,l,o),hu(),!1}if(gt)return e=Ce.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=o,l!==co&&(t=Error(r(422),{cause:l}),Oi(Me(t,n)))):(l!==co&&(e=Error(r(423),{cause:l}),Oi(Me(e,n))),t=t.current.alternate,t.flags|=65536,o&=-o,t.lanes|=o,l=Me(l,n),o=Go(t.stateNode,l,o),So(t,o),zt!==4&&(zt=2)),!1;var u=Error(r(520),{cause:l});if(u=Me(u,n),Ii===null?Ii=[u]:Ii.push(u),zt!==4&&(zt=2),e===null)return!0;l=Me(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=o&-o,n.lanes|=t,t=Go(n.stateNode,l,t),So(n,t),!1;case 1:if(e=n.type,u=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(_n===null||!_n.has(u))))return n.flags|=65536,o&=-o,n.lanes|=o,o=Ch(o),Nh(o,t,n,l),So(n,o),!1}n=n.return}while(n!==null);return!1}var jh=Error(r(461)),Qt=!1;function Ft(t,e,n,l){e.child=t===null?Sh(e,null,n,l):Xa(e,t.child,n,l)}function Oh(t,e,n,l,o){n=n.render;var u=e.ref;if("ref"in l){var y={};for(var v in l)v!=="ref"&&(y[v]=l[v])}else y=l;return sa(e),l=wo(t,e,n,y,u,o),v=Do(),t!==null&&!Qt?(Ro(t,e,o),on(t,e,o)):(gt&&v&&oo(e),e.flags|=1,Ft(t,e,l,o),e.child)}function Vh(t,e,n,l,o){if(t===null){var u=n.type;return typeof u=="function"&&!lo(u)&&u.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=u,zh(t,e,u,l,o)):(t=ss(n.type,null,l,e,e.mode,o),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!Fo(t,o)){var y=u.memoizedProps;if(n=n.compare,n=n!==null?n:Ri,n(y,l)&&t.ref===e.ref)return on(t,e,o)}return e.flags|=1,t=tn(u,l),t.ref=e.ref,t.return=e,e.child=t}function zh(t,e,n,l,o){if(t!==null){var u=t.memoizedProps;if(Ri(u,l)&&t.ref===e.ref)if(Qt=!1,e.pendingProps=l=u,Fo(t,o))(t.flags&131072)!==0&&(Qt=!0);else return e.lanes=t.lanes,on(t,e,o)}return Xo(t,e,n,l,o)}function _h(t,e,n){var l=e.pendingProps,o=l.children,u=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if((e.flags&128)!==0){if(l=u!==null?u.baseLanes|n:n,t!==null){for(o=e.child=t.child,u=0;o!==null;)u=u|o.lanes|o.childLanes,o=o.sibling;e.childLanes=u&~l}else e.childLanes=0,e.child=null;return Uh(t,e,l,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&fs(e,u!==null?u.cachePool:null),u!==null?zd(e,u):Ao(),Th(e);else return e.lanes=e.childLanes=536870912,Uh(t,e,u!==null?u.baseLanes|n:n,n)}else u!==null?(fs(e,u.cachePool),zd(e,u),Cn(),e.memoizedState=null):(t!==null&&fs(e,null),Ao(),Cn());return Ft(t,e,o,n),e.child}function Uh(t,e,n,l){var o=go();return o=o===null?null:{parent:Xt._currentValue,pool:o},e.memoizedState={baseLanes:n,cachePool:o},t!==null&&fs(e,null),Ao(),Th(e),t!==null&&Vi(t,e,l,!0),null}function Rs(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(r(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function Xo(t,e,n,l,o){return sa(e),n=wo(t,e,n,l,void 0,o),l=Do(),t!==null&&!Qt?(Ro(t,e,o),on(t,e,o)):(gt&&l&&oo(e),e.flags|=1,Ft(t,e,n,o),e.child)}function Bh(t,e,n,l,o,u){return sa(e),e.updateQueue=null,n=Ud(e,l,n,o),_d(t),l=Do(),t!==null&&!Qt?(Ro(t,e,u),on(t,e,u)):(gt&&l&&oo(e),e.flags|=1,Ft(t,e,n,u),e.child)}function Lh(t,e,n,l,o){if(sa(e),e.stateNode===null){var u=za,y=n.contextType;typeof y=="object"&&y!==null&&(u=ne(y)),u=new n(l,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=Yo,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=l,u.state=e.memoizedState,u.refs={},xo(e),y=n.contextType,u.context=typeof y=="object"&&y!==null?ne(y):za,u.state=e.memoizedState,y=n.getDerivedStateFromProps,typeof y=="function"&&(qo(e,n,y,l),u.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(y=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),y!==u.state&&Yo.enqueueReplaceState(u,u.state,null),ki(e,l,u,o),Hi(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){u=e.stateNode;var v=e.memoizedProps,T=ua(n,v);u.props=T;var N=u.context,_=n.contextType;y=za,typeof _=="object"&&_!==null&&(y=ne(_));var H=n.getDerivedStateFromProps;_=typeof H=="function"||typeof u.getSnapshotBeforeUpdate=="function",v=e.pendingProps!==v,_||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(v||N!==y)&&Eh(e,u,l,y),En=!1;var j=e.memoizedState;u.state=j,ki(e,l,u,o),Hi(),N=e.memoizedState,v||j!==N||En?(typeof H=="function"&&(qo(e,n,H,l),N=e.memoizedState),(T=En||Ah(e,n,T,l,j,N,y))?(_||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=N),u.props=l,u.state=N,u.context=y,l=T):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{u=e.stateNode,bo(t,e),y=e.memoizedProps,_=ua(n,y),u.props=_,H=e.pendingProps,j=u.context,N=n.contextType,T=za,typeof N=="object"&&N!==null&&(T=ne(N)),v=n.getDerivedStateFromProps,(N=typeof v=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(y!==H||j!==T)&&Eh(e,u,l,T),En=!1,j=e.memoizedState,u.state=j,ki(e,l,u,o),Hi();var O=e.memoizedState;y!==H||j!==O||En||t!==null&&t.dependencies!==null&&us(t.dependencies)?(typeof v=="function"&&(qo(e,n,v,l),O=e.memoizedState),(_=En||Ah(e,n,_,l,j,O,T)||t!==null&&t.dependencies!==null&&us(t.dependencies))?(N||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(l,O,T),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(l,O,T)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||y===t.memoizedProps&&j===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&j===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=O),u.props=l,u.state=O,u.context=T,l=_):(typeof u.componentDidUpdate!="function"||y===t.memoizedProps&&j===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&j===t.memoizedState||(e.flags|=1024),l=!1)}return u=l,Rs(t,e),l=(e.flags&128)!==0,u||l?(u=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&l?(e.child=Xa(e,t.child,null,o),e.child=Xa(e,null,n,o)):Ft(t,e,n,o),e.memoizedState=u.state,t=e.child):t=on(t,e,o),t}function Hh(t,e,n,l){return ji(),e.flags|=256,Ft(t,e,n,l),e.child}var Ko={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Zo(t){return{baseLanes:t,cachePool:wd()}}function Qo(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Ne),t}function kh(t,e,n){var l=e.pendingProps,o=!1,u=(e.flags&128)!==0,y;if((y=u)||(y=t!==null&&t.memoizedState===null?!1:(Kt.current&2)!==0),y&&(o=!0,e.flags&=-129),y=(e.flags&32)!==0,e.flags&=-33,t===null){if(gt){if(o?Rn(e):Cn(),gt){var v=Vt,T;if(T=v){t:{for(T=v,v=qe;T.nodeType!==8;){if(!v){v=null;break t}if(T=Be(T.nextSibling),T===null){v=null;break t}}v=T}v!==null?(e.memoizedState={dehydrated:v,treeContext:ea!==null?{id:en,overflow:nn}:null,retryLane:536870912,hydrationErrors:null},T=ge(18,null,null,0),T.stateNode=v,T.return=e,e.child=T,le=e,Vt=null,T=!0):T=!1}T||ia(e)}if(v=e.memoizedState,v!==null&&(v=v.dehydrated,v!==null))return Nu(v)?e.lanes=32:e.lanes=536870912,null;rn(e)}return v=l.children,l=l.fallback,o?(Cn(),o=e.mode,v=Cs({mode:"hidden",children:v},o),l=ta(l,o,n,null),v.return=e,l.return=e,v.sibling=l,e.child=v,o=e.child,o.memoizedState=Zo(n),o.childLanes=Qo(t,y,n),e.memoizedState=Ko,l):(Rn(e),Po(e,v))}if(T=t.memoizedState,T!==null&&(v=T.dehydrated,v!==null)){if(u)e.flags&256?(Rn(e),e.flags&=-257,e=$o(t,e,n)):e.memoizedState!==null?(Cn(),e.child=t.child,e.flags|=128,e=null):(Cn(),o=l.fallback,v=e.mode,l=Cs({mode:"visible",children:l.children},v),o=ta(o,v,n,null),o.flags|=2,l.return=e,o.return=e,l.sibling=o,e.child=l,Xa(e,t.child,null,n),l=e.child,l.memoizedState=Zo(n),l.childLanes=Qo(t,y,n),e.memoizedState=Ko,e=o);else if(Rn(e),Nu(v)){if(y=v.nextSibling&&v.nextSibling.dataset,y)var N=y.dgst;y=N,l=Error(r(419)),l.stack="",l.digest=y,Oi({value:l,source:null,stack:null}),e=$o(t,e,n)}else if(Qt||Vi(t,e,n,!1),y=(n&t.childLanes)!==0,Qt||y){if(y=Mt,y!==null&&(l=n&-n,l=(l&42)!==0?1:Nr(l),l=(l&(y.suspendedLanes|n))!==0?0:l,l!==0&&l!==T.retryLane))throw T.retryLane=l,Va(t,l),Te(y,t,l),jh;v.data==="$?"||hu(),e=$o(t,e,n)}else v.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=T.treeContext,Vt=Be(v.nextSibling),le=e,gt=!0,aa=null,qe=!1,t!==null&&(De[Re++]=en,De[Re++]=nn,De[Re++]=ea,en=t.id,nn=t.overflow,ea=e),e=Po(e,l.children),e.flags|=4096);return e}return o?(Cn(),o=l.fallback,v=e.mode,T=t.child,N=T.sibling,l=tn(T,{mode:"hidden",children:l.children}),l.subtreeFlags=T.subtreeFlags&65011712,N!==null?o=tn(N,o):(o=ta(o,v,n,null),o.flags|=2),o.return=e,l.return=e,l.sibling=o,e.child=l,l=o,o=e.child,v=t.child.memoizedState,v===null?v=Zo(n):(T=v.cachePool,T!==null?(N=Xt._currentValue,T=T.parent!==N?{parent:N,pool:N}:T):T=wd(),v={baseLanes:v.baseLanes|n,cachePool:T}),o.memoizedState=v,o.childLanes=Qo(t,y,n),e.memoizedState=Ko,l):(Rn(e),n=t.child,t=n.sibling,n=tn(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(y=e.deletions,y===null?(e.deletions=[t],e.flags|=16):y.push(t)),e.child=n,e.memoizedState=null,n)}function Po(t,e){return e=Cs({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Cs(t,e){return t=ge(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function $o(t,e,n){return Xa(e,t.child,null,n),t=Po(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function qh(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),ho(t.return,e,n)}function Jo(t,e,n,l,o){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:o}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=l,u.tail=n,u.tailMode=o)}function Yh(t,e,n){var l=e.pendingProps,o=l.revealOrder,u=l.tail;if(Ft(t,e,l.children,n),l=Kt.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&qh(t,n,e);else if(t.tag===19)qh(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(K(Kt,l),o){case"forwards":for(n=e.child,o=null;n!==null;)t=n.alternate,t!==null&&Ms(t)===null&&(o=n),n=n.sibling;n=o,n===null?(o=e.child,e.child=null):(o=n.sibling,n.sibling=null),Jo(e,!1,o,n,u);break;case"backwards":for(n=null,o=e.child,e.child=null;o!==null;){if(t=o.alternate,t!==null&&Ms(t)===null){e.child=o;break}t=o.sibling,o.sibling=n,n=o,o=t}Jo(e,!0,n,null,u);break;case"together":Jo(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function on(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),zn|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(Vi(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(r(153));if(e.child!==null){for(t=e.child,n=tn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=tn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Fo(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&us(t)))}function a1(t,e,n){switch(e.tag){case 3:Dt(e,e.stateNode.containerInfo),An(e,Xt,t.memoizedState.cache),ji();break;case 27:case 5:Mr(e);break;case 4:Dt(e,e.stateNode.containerInfo);break;case 10:An(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(Rn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?kh(t,e,n):(Rn(e),t=on(t,e,n),t!==null?t.sibling:null);Rn(e);break;case 19:var o=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(Vi(t,e,n,!1),l=(n&e.childLanes)!==0),o){if(l)return Yh(t,e,n);e.flags|=128}if(o=e.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),K(Kt,Kt.current),l)break;return null;case 22:case 23:return e.lanes=0,_h(t,e,n);case 24:An(e,Xt,t.memoizedState.cache)}return on(t,e,n)}function Gh(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Qt=!0;else{if(!Fo(t,n)&&(e.flags&128)===0)return Qt=!1,a1(t,e,n);Qt=(t.flags&131072)!==0}else Qt=!1,gt&&(e.flags&1048576)!==0&&xd(e,os,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,o=l._init;if(l=o(l._payload),e.type=l,typeof l=="function")lo(l)?(t=ua(l,t),e.tag=1,e=Lh(null,e,l,t,n)):(e.tag=0,e=Xo(null,e,l,t,n));else{if(l!=null){if(o=l.$$typeof,o===P){e.tag=11,e=Oh(null,e,l,t,n);break t}else if(o===lt){e.tag=14,e=Vh(null,e,l,t,n);break t}}throw e=He(l)||l,Error(r(306,e,""))}}return e;case 0:return Xo(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,o=ua(l,e.pendingProps),Lh(t,e,l,o,n);case 3:t:{if(Dt(e,e.stateNode.containerInfo),t===null)throw Error(r(387));l=e.pendingProps;var u=e.memoizedState;o=u.element,bo(t,e),ki(e,l,null,n);var y=e.memoizedState;if(l=y.cache,An(e,Xt,l),l!==u.cache&&mo(e,[Xt],n,!0),Hi(),l=y.element,u.isDehydrated)if(u={element:l,isDehydrated:!1,cache:y.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=Hh(t,e,l,n);break t}else if(l!==o){o=Me(Error(r(424)),e),Oi(o),e=Hh(t,e,l,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Vt=Be(t.firstChild),le=e,gt=!0,aa=null,qe=!0,n=Sh(e,null,l,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(ji(),l===o){e=on(t,e,n);break t}Ft(t,e,l,n)}e=e.child}return e;case 26:return Rs(t,e),t===null?(n=Qm(e.type,null,e.pendingProps,null))?e.memoizedState=n:gt||(n=e.type,t=e.pendingProps,l=Gs(it.current).createElement(n),l[ee]=e,l[re]=t,It(l,n,t),Zt(l),e.stateNode=l):e.memoizedState=Qm(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Mr(e),t===null&&gt&&(l=e.stateNode=Xm(e.type,e.pendingProps,it.current),le=e,qe=!0,o=Vt,Ln(e.type)?(ju=o,Vt=Be(l.firstChild)):Vt=o),Ft(t,e,e.pendingProps.children,n),Rs(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&gt&&((o=l=Vt)&&(l=j1(l,e.type,e.pendingProps,qe),l!==null?(e.stateNode=l,le=e,Vt=Be(l.firstChild),qe=!1,o=!0):o=!1),o||ia(e)),Mr(e),o=e.type,u=e.pendingProps,y=t!==null?t.memoizedProps:null,l=u.children,Du(o,u)?l=null:y!==null&&Du(o,y)&&(e.flags|=32),e.memoizedState!==null&&(o=wo(t,e,$v,null,null,n),ol._currentValue=o),Rs(t,e),Ft(t,e,l,n),e.child;case 6:return t===null&&gt&&((t=n=Vt)&&(n=O1(n,e.pendingProps,qe),n!==null?(e.stateNode=n,le=e,Vt=null,t=!0):t=!1),t||ia(e)),null;case 13:return kh(t,e,n);case 4:return Dt(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=Xa(e,null,l,n):Ft(t,e,l,n),e.child;case 11:return Oh(t,e,e.type,e.pendingProps,n);case 7:return Ft(t,e,e.pendingProps,n),e.child;case 8:return Ft(t,e,e.pendingProps.children,n),e.child;case 12:return Ft(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,An(e,e.type,l.value),Ft(t,e,l.children,n),e.child;case 9:return o=e.type._context,l=e.pendingProps.children,sa(e),o=ne(o),l=l(o),e.flags|=1,Ft(t,e,l,n),e.child;case 14:return Vh(t,e,e.type,e.pendingProps,n);case 15:return zh(t,e,e.type,e.pendingProps,n);case 19:return Yh(t,e,n);case 31:return l=e.pendingProps,n=e.mode,l={mode:l.mode,children:l.children},t===null?(n=Cs(l,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=tn(t.child,l),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return _h(t,e,n);case 24:return sa(e),l=ne(Xt),t===null?(o=go(),o===null&&(o=Mt,u=po(),o.pooledCache=u,u.refCount++,u!==null&&(o.pooledCacheLanes|=n),o=u),e.memoizedState={parent:l,cache:o},xo(e),An(e,Xt,o)):((t.lanes&n)!==0&&(bo(t,e),ki(e,null,null,n),Hi()),o=t.memoizedState,u=e.memoizedState,o.parent!==l?(o={parent:l,cache:l},e.memoizedState=o,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=o),An(e,Xt,l)):(l=u.cache,An(e,Xt,l),l!==o.cache&&mo(e,[Xt],n,!0))),Ft(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(r(156,e.tag))}function un(t){t.flags|=4}function Xh(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Wm(e)){if(e=Ce.current,e!==null&&((ht&4194048)===ht?Ye!==null:(ht&62914560)!==ht&&(ht&536870912)===0||e!==Ye))throw Bi=vo,Dd;t.flags|=8192}}function Ns(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Tf():536870912,t.lanes|=e,Pa|=e)}function Qi(t,e){if(!gt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function Ct(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var o=t.child;o!==null;)n|=o.lanes|o.childLanes,l|=o.subtreeFlags&65011712,l|=o.flags&65011712,o.return=t,o=o.sibling;else for(o=t.child;o!==null;)n|=o.lanes|o.childLanes,l|=o.subtreeFlags,l|=o.flags,o.return=t,o=o.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function i1(t,e,n){var l=e.pendingProps;switch(uo(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ct(e),null;case 1:return Ct(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),ln(Xt),xn(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(Ni(e)?un(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Td())),Ct(e),null;case 26:return n=e.memoizedState,t===null?(un(e),n!==null?(Ct(e),Xh(e,n)):(Ct(e),e.flags&=-16777217)):n?n!==t.memoizedState?(un(e),Ct(e),Xh(e,n)):(Ct(e),e.flags&=-16777217):(t.memoizedProps!==l&&un(e),Ct(e),e.flags&=-16777217),null;case 27:ql(e),n=it.current;var o=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&un(e);else{if(!l){if(e.stateNode===null)throw Error(r(166));return Ct(e),null}t=W.current,Ni(e)?bd(e):(t=Xm(o,l,n),e.stateNode=t,un(e))}return Ct(e),null;case 5:if(ql(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&un(e);else{if(!l){if(e.stateNode===null)throw Error(r(166));return Ct(e),null}if(t=W.current,Ni(e))bd(e);else{switch(o=Gs(it.current),t){case 1:t=o.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=o.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?o.createElement("select",{is:l.is}):o.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?o.createElement(n,{is:l.is}):o.createElement(n)}}t[ee]=e,t[re]=l;t:for(o=e.child;o!==null;){if(o.tag===5||o.tag===6)t.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===e)break t;for(;o.sibling===null;){if(o.return===null||o.return===e)break t;o=o.return}o.sibling.return=o.return,o=o.sibling}e.stateNode=t;t:switch(It(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&un(e)}}return Ct(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&un(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(r(166));if(t=it.current,Ni(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,o=le,o!==null)switch(o.tag){case 27:case 5:l=o.memoizedProps}t[ee]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||Bm(t.nodeValue,n)),t||ia(e)}else t=Gs(t).createTextNode(l),t[ee]=e,e.stateNode=t}return Ct(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(o=Ni(e),l!==null&&l.dehydrated!==null){if(t===null){if(!o)throw Error(r(318));if(o=e.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(r(317));o[ee]=e}else ji(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Ct(e),o=!1}else o=Td(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=o),o=!0;if(!o)return e.flags&256?(rn(e),e):(rn(e),null)}if(rn(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,o=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(o=l.alternate.memoizedState.cachePool.pool);var u=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(u=l.memoizedState.cachePool.pool),u!==o&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Ns(e,e.updateQueue),Ct(e),null;case 4:return xn(),t===null&&Tu(e.stateNode.containerInfo),Ct(e),null;case 10:return ln(e.type),Ct(e),null;case 19:if(Z(Kt),o=e.memoizedState,o===null)return Ct(e),null;if(l=(e.flags&128)!==0,u=o.rendering,u===null)if(l)Qi(o,!1);else{if(zt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=Ms(t),u!==null){for(e.flags|=128,Qi(o,!1),t=u.updateQueue,e.updateQueue=t,Ns(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)vd(n,t),n=n.sibling;return K(Kt,Kt.current&1|2),e.child}t=t.sibling}o.tail!==null&&ke()>Vs&&(e.flags|=128,l=!0,Qi(o,!1),e.lanes=4194304)}else{if(!l)if(t=Ms(u),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Ns(e,t),Qi(o,!0),o.tail===null&&o.tailMode==="hidden"&&!u.alternate&&!gt)return Ct(e),null}else 2*ke()-o.renderingStartTime>Vs&&n!==536870912&&(e.flags|=128,l=!0,Qi(o,!1),e.lanes=4194304);o.isBackwards?(u.sibling=e.child,e.child=u):(t=o.last,t!==null?t.sibling=u:e.child=u,o.last=u)}return o.tail!==null?(e=o.tail,o.rendering=e,o.tail=e.sibling,o.renderingStartTime=ke(),e.sibling=null,t=Kt.current,K(Kt,l?t&1|2:t&1),e):(Ct(e),null);case 22:case 23:return rn(e),Eo(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(n&536870912)!==0&&(e.flags&128)===0&&(Ct(e),e.subtreeFlags&6&&(e.flags|=8192)):Ct(e),n=e.updateQueue,n!==null&&Ns(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&Z(ra),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),ln(Xt),Ct(e),null;case 25:return null;case 30:return null}throw Error(r(156,e.tag))}function l1(t,e){switch(uo(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return ln(Xt),xn(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return ql(e),null;case 13:if(rn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(r(340));ji()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Z(Kt),null;case 4:return xn(),null;case 10:return ln(e.type),null;case 22:case 23:return rn(e),Eo(),t!==null&&Z(ra),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return ln(Xt),null;case 25:return null;default:return null}}function Kh(t,e){switch(uo(e),e.tag){case 3:ln(Xt),xn();break;case 26:case 27:case 5:ql(e);break;case 4:xn();break;case 13:rn(e);break;case 19:Z(Kt);break;case 10:ln(e.type);break;case 22:case 23:rn(e),Eo(),t!==null&&Z(ra);break;case 24:ln(Xt)}}function Pi(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var o=l.next;n=o;do{if((n.tag&t)===t){l=void 0;var u=n.create,y=n.inst;l=u(),y.destroy=l}n=n.next}while(n!==o)}}catch(v){Et(e,e.return,v)}}function Nn(t,e,n){try{var l=e.updateQueue,o=l!==null?l.lastEffect:null;if(o!==null){var u=o.next;l=u;do{if((l.tag&t)===t){var y=l.inst,v=y.destroy;if(v!==void 0){y.destroy=void 0,o=e;var T=n,N=v;try{N()}catch(_){Et(o,T,_)}}}l=l.next}while(l!==u)}}catch(_){Et(e,e.return,_)}}function Zh(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Vd(e,n)}catch(l){Et(t,t.return,l)}}}function Qh(t,e,n){n.props=ua(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){Et(t,e,l)}}function $i(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof n=="function"?t.refCleanup=n(l):n.current=l}}catch(o){Et(t,e,o)}}function Ge(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(o){Et(t,e,o)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(o){Et(t,e,o)}else n.current=null}function Ph(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(o){Et(t,t.return,o)}}function Wo(t,e,n){try{var l=t.stateNode;w1(l,t.type,n,e),l[re]=e}catch(o){Et(t,t.return,o)}}function $h(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Ln(t.type)||t.tag===4}function Io(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||$h(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Ln(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function tu(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=Ys));else if(l!==4&&(l===27&&Ln(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(tu(t,e,n),t=t.sibling;t!==null;)tu(t,e,n),t=t.sibling}function js(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&(l===27&&Ln(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(js(t,e,n),t=t.sibling;t!==null;)js(t,e,n),t=t.sibling}function Jh(t){var e=t.stateNode,n=t.memoizedProps;try{for(var l=t.type,o=e.attributes;o.length;)e.removeAttributeNode(o[0]);It(e,l,n),e[ee]=t,e[re]=n}catch(u){Et(t,t.return,u)}}var cn=!1,Bt=!1,eu=!1,Fh=typeof WeakSet=="function"?WeakSet:Set,Pt=null;function s1(t,e){if(t=t.containerInfo,Mu=$s,t=od(t),Wr(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var o=l.anchorOffset,u=l.focusNode;l=l.focusOffset;try{n.nodeType,u.nodeType}catch{n=null;break t}var y=0,v=-1,T=-1,N=0,_=0,H=t,j=null;e:for(;;){for(var O;H!==n||o!==0&&H.nodeType!==3||(v=y+o),H!==u||l!==0&&H.nodeType!==3||(T=y+l),H.nodeType===3&&(y+=H.nodeValue.length),(O=H.firstChild)!==null;)j=H,H=O;for(;;){if(H===t)break e;if(j===n&&++N===o&&(v=y),j===u&&++_===l&&(T=y),(O=H.nextSibling)!==null)break;H=j,j=H.parentNode}H=O}n=v===-1||T===-1?null:{start:v,end:T}}else n=null}n=n||{start:0,end:0}}else n=null;for(wu={focusedElem:t,selectionRange:n},$s=!1,Pt=e;Pt!==null;)if(e=Pt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Pt=t;else for(;Pt!==null;){switch(e=Pt,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,n=e,o=u.memoizedProps,u=u.memoizedState,l=n.stateNode;try{var et=ua(n.type,o,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(et,u),l.__reactInternalSnapshotBeforeUpdate=t}catch(I){Et(n,n.return,I)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)Cu(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Cu(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(r(163))}if(t=e.sibling,t!==null){t.return=e.return,Pt=t;break}Pt=e.return}}function Wh(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:jn(t,n),l&4&&Pi(5,n);break;case 1:if(jn(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(y){Et(n,n.return,y)}else{var o=ua(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(o,e,t.__reactInternalSnapshotBeforeUpdate)}catch(y){Et(n,n.return,y)}}l&64&&Zh(n),l&512&&$i(n,n.return);break;case 3:if(jn(t,n),l&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{Vd(t,e)}catch(y){Et(n,n.return,y)}}break;case 27:e===null&&l&4&&Jh(n);case 26:case 5:jn(t,n),e===null&&l&4&&Ph(n),l&512&&$i(n,n.return);break;case 12:jn(t,n);break;case 13:jn(t,n),l&4&&em(t,n),l&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=p1.bind(null,n),V1(t,n))));break;case 22:if(l=n.memoizedState!==null||cn,!l){e=e!==null&&e.memoizedState!==null||Bt,o=cn;var u=Bt;cn=l,(Bt=e)&&!u?On(t,n,(n.subtreeFlags&8772)!==0):jn(t,n),cn=o,Bt=u}break;case 30:break;default:jn(t,n)}}function Ih(t){var e=t.alternate;e!==null&&(t.alternate=null,Ih(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Vr(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Rt=null,ce=!1;function fn(t,e,n){for(n=n.child;n!==null;)tm(t,e,n),n=n.sibling}function tm(t,e,n){if(me&&typeof me.onCommitFiberUnmount=="function")try{me.onCommitFiberUnmount(yi,n)}catch{}switch(n.tag){case 26:Bt||Ge(n,e),fn(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Bt||Ge(n,e);var l=Rt,o=ce;Ln(n.type)&&(Rt=n.stateNode,ce=!1),fn(t,e,n),il(n.stateNode),Rt=l,ce=o;break;case 5:Bt||Ge(n,e);case 6:if(l=Rt,o=ce,Rt=null,fn(t,e,n),Rt=l,ce=o,Rt!==null)if(ce)try{(Rt.nodeType===9?Rt.body:Rt.nodeName==="HTML"?Rt.ownerDocument.body:Rt).removeChild(n.stateNode)}catch(u){Et(n,e,u)}else try{Rt.removeChild(n.stateNode)}catch(u){Et(n,e,u)}break;case 18:Rt!==null&&(ce?(t=Rt,Ym(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),dl(t)):Ym(Rt,n.stateNode));break;case 4:l=Rt,o=ce,Rt=n.stateNode.containerInfo,ce=!0,fn(t,e,n),Rt=l,ce=o;break;case 0:case 11:case 14:case 15:Bt||Nn(2,n,e),Bt||Nn(4,n,e),fn(t,e,n);break;case 1:Bt||(Ge(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Qh(n,e,l)),fn(t,e,n);break;case 21:fn(t,e,n);break;case 22:Bt=(l=Bt)||n.memoizedState!==null,fn(t,e,n),Bt=l;break;default:fn(t,e,n)}}function em(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{dl(t)}catch(n){Et(e,e.return,n)}}function r1(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Fh),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Fh),e;default:throw Error(r(435,t.tag))}}function nu(t,e){var n=r1(t);e.forEach(function(l){var o=y1.bind(null,t,l);n.has(l)||(n.add(l),l.then(o,o))})}function ve(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var o=n[l],u=t,y=e,v=y;t:for(;v!==null;){switch(v.tag){case 27:if(Ln(v.type)){Rt=v.stateNode,ce=!1;break t}break;case 5:Rt=v.stateNode,ce=!1;break t;case 3:case 4:Rt=v.stateNode.containerInfo,ce=!0;break t}v=v.return}if(Rt===null)throw Error(r(160));tm(u,y,o),Rt=null,ce=!1,u=o.alternate,u!==null&&(u.return=null),o.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)nm(e,t),e=e.sibling}var Ue=null;function nm(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:ve(e,t),xe(t),l&4&&(Nn(3,t,t.return),Pi(3,t),Nn(5,t,t.return));break;case 1:ve(e,t),xe(t),l&512&&(Bt||n===null||Ge(n,n.return)),l&64&&cn&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var o=Ue;if(ve(e,t),xe(t),l&512&&(Bt||n===null||Ge(n,n.return)),l&4){var u=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,o=o.ownerDocument||o;e:switch(l){case"title":u=o.getElementsByTagName("title")[0],(!u||u[xi]||u[ee]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=o.createElement(l),o.head.insertBefore(u,o.querySelector("head > title"))),It(u,l,n),u[ee]=t,Zt(u),l=u;break t;case"link":var y=Jm("link","href",o).get(l+(n.href||""));if(y){for(var v=0;v<y.length;v++)if(u=y[v],u.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&u.getAttribute("rel")===(n.rel==null?null:n.rel)&&u.getAttribute("title")===(n.title==null?null:n.title)&&u.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){y.splice(v,1);break e}}u=o.createElement(l),It(u,l,n),o.head.appendChild(u);break;case"meta":if(y=Jm("meta","content",o).get(l+(n.content||""))){for(v=0;v<y.length;v++)if(u=y[v],u.getAttribute("content")===(n.content==null?null:""+n.content)&&u.getAttribute("name")===(n.name==null?null:n.name)&&u.getAttribute("property")===(n.property==null?null:n.property)&&u.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&u.getAttribute("charset")===(n.charSet==null?null:n.charSet)){y.splice(v,1);break e}}u=o.createElement(l),It(u,l,n),o.head.appendChild(u);break;default:throw Error(r(468,l))}u[ee]=t,Zt(u),l=u}t.stateNode=l}else Fm(o,t.type,t.stateNode);else t.stateNode=$m(o,l,t.memoizedProps);else u!==l?(u===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):u.count--,l===null?Fm(o,t.type,t.stateNode):$m(o,l,t.memoizedProps)):l===null&&t.stateNode!==null&&Wo(t,t.memoizedProps,n.memoizedProps)}break;case 27:ve(e,t),xe(t),l&512&&(Bt||n===null||Ge(n,n.return)),n!==null&&l&4&&Wo(t,t.memoizedProps,n.memoizedProps);break;case 5:if(ve(e,t),xe(t),l&512&&(Bt||n===null||Ge(n,n.return)),t.flags&32){o=t.stateNode;try{wa(o,"")}catch(O){Et(t,t.return,O)}}l&4&&t.stateNode!=null&&(o=t.memoizedProps,Wo(t,o,n!==null?n.memoizedProps:o)),l&1024&&(eu=!0);break;case 6:if(ve(e,t),xe(t),l&4){if(t.stateNode===null)throw Error(r(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(O){Et(t,t.return,O)}}break;case 3:if(Zs=null,o=Ue,Ue=Xs(e.containerInfo),ve(e,t),Ue=o,xe(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{dl(e.containerInfo)}catch(O){Et(t,t.return,O)}eu&&(eu=!1,am(t));break;case 4:l=Ue,Ue=Xs(t.stateNode.containerInfo),ve(e,t),xe(t),Ue=l;break;case 12:ve(e,t),xe(t);break;case 13:ve(e,t),xe(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(ou=ke()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,nu(t,l)));break;case 22:o=t.memoizedState!==null;var T=n!==null&&n.memoizedState!==null,N=cn,_=Bt;if(cn=N||o,Bt=_||T,ve(e,t),Bt=_,cn=N,xe(t),l&8192)t:for(e=t.stateNode,e._visibility=o?e._visibility&-2:e._visibility|1,o&&(n===null||T||cn||Bt||ca(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){T=n=e;try{if(u=T.stateNode,o)y=u.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{v=T.stateNode;var H=T.memoizedProps.style,j=H!=null&&H.hasOwnProperty("display")?H.display:null;v.style.display=j==null||typeof j=="boolean"?"":(""+j).trim()}}catch(O){Et(T,T.return,O)}}}else if(e.tag===6){if(n===null){T=e;try{T.stateNode.nodeValue=o?"":T.memoizedProps}catch(O){Et(T,T.return,O)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,nu(t,n))));break;case 19:ve(e,t),xe(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,nu(t,l)));break;case 30:break;case 21:break;default:ve(e,t),xe(t)}}function xe(t){var e=t.flags;if(e&2){try{for(var n,l=t.return;l!==null;){if($h(l)){n=l;break}l=l.return}if(n==null)throw Error(r(160));switch(n.tag){case 27:var o=n.stateNode,u=Io(t);js(t,u,o);break;case 5:var y=n.stateNode;n.flags&32&&(wa(y,""),n.flags&=-33);var v=Io(t);js(t,v,y);break;case 3:case 4:var T=n.stateNode.containerInfo,N=Io(t);tu(t,N,T);break;default:throw Error(r(161))}}catch(_){Et(t,t.return,_)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function am(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;am(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function jn(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Wh(t,e.alternate,e),e=e.sibling}function ca(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Nn(4,e,e.return),ca(e);break;case 1:Ge(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&Qh(e,e.return,n),ca(e);break;case 27:il(e.stateNode);case 26:case 5:Ge(e,e.return),ca(e);break;case 22:e.memoizedState===null&&ca(e);break;case 30:ca(e);break;default:ca(e)}t=t.sibling}}function On(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,o=t,u=e,y=u.flags;switch(u.tag){case 0:case 11:case 15:On(o,u,n),Pi(4,u);break;case 1:if(On(o,u,n),l=u,o=l.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(N){Et(l,l.return,N)}if(l=u,o=l.updateQueue,o!==null){var v=l.stateNode;try{var T=o.shared.hiddenCallbacks;if(T!==null)for(o.shared.hiddenCallbacks=null,o=0;o<T.length;o++)Od(T[o],v)}catch(N){Et(l,l.return,N)}}n&&y&64&&Zh(u),$i(u,u.return);break;case 27:Jh(u);case 26:case 5:On(o,u,n),n&&l===null&&y&4&&Ph(u),$i(u,u.return);break;case 12:On(o,u,n);break;case 13:On(o,u,n),n&&y&4&&em(o,u);break;case 22:u.memoizedState===null&&On(o,u,n),$i(u,u.return);break;case 30:break;default:On(o,u,n)}e=e.sibling}}function au(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&zi(n))}function iu(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&zi(t))}function Xe(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)im(t,e,n,l),e=e.sibling}function im(t,e,n,l){var o=e.flags;switch(e.tag){case 0:case 11:case 15:Xe(t,e,n,l),o&2048&&Pi(9,e);break;case 1:Xe(t,e,n,l);break;case 3:Xe(t,e,n,l),o&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&zi(t)));break;case 12:if(o&2048){Xe(t,e,n,l),t=e.stateNode;try{var u=e.memoizedProps,y=u.id,v=u.onPostCommit;typeof v=="function"&&v(y,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(T){Et(e,e.return,T)}}else Xe(t,e,n,l);break;case 13:Xe(t,e,n,l);break;case 23:break;case 22:u=e.stateNode,y=e.alternate,e.memoizedState!==null?u._visibility&2?Xe(t,e,n,l):Ji(t,e):u._visibility&2?Xe(t,e,n,l):(u._visibility|=2,Ka(t,e,n,l,(e.subtreeFlags&10256)!==0)),o&2048&&au(y,e);break;case 24:Xe(t,e,n,l),o&2048&&iu(e.alternate,e);break;default:Xe(t,e,n,l)}}function Ka(t,e,n,l,o){for(o=o&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,y=e,v=n,T=l,N=y.flags;switch(y.tag){case 0:case 11:case 15:Ka(u,y,v,T,o),Pi(8,y);break;case 23:break;case 22:var _=y.stateNode;y.memoizedState!==null?_._visibility&2?Ka(u,y,v,T,o):Ji(u,y):(_._visibility|=2,Ka(u,y,v,T,o)),o&&N&2048&&au(y.alternate,y);break;case 24:Ka(u,y,v,T,o),o&&N&2048&&iu(y.alternate,y);break;default:Ka(u,y,v,T,o)}e=e.sibling}}function Ji(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,o=l.flags;switch(l.tag){case 22:Ji(n,l),o&2048&&au(l.alternate,l);break;case 24:Ji(n,l),o&2048&&iu(l.alternate,l);break;default:Ji(n,l)}e=e.sibling}}var Fi=8192;function Za(t){if(t.subtreeFlags&Fi)for(t=t.child;t!==null;)lm(t),t=t.sibling}function lm(t){switch(t.tag){case 26:Za(t),t.flags&Fi&&t.memoizedState!==null&&Z1(Ue,t.memoizedState,t.memoizedProps);break;case 5:Za(t);break;case 3:case 4:var e=Ue;Ue=Xs(t.stateNode.containerInfo),Za(t),Ue=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Fi,Fi=16777216,Za(t),Fi=e):Za(t));break;default:Za(t)}}function sm(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Wi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Pt=l,om(l,t)}sm(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)rm(t),t=t.sibling}function rm(t){switch(t.tag){case 0:case 11:case 15:Wi(t),t.flags&2048&&Nn(9,t,t.return);break;case 3:Wi(t);break;case 12:Wi(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Os(t)):Wi(t);break;default:Wi(t)}}function Os(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Pt=l,om(l,t)}sm(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Nn(8,e,e.return),Os(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,Os(e));break;default:Os(e)}t=t.sibling}}function om(t,e){for(;Pt!==null;){var n=Pt;switch(n.tag){case 0:case 11:case 15:Nn(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:zi(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,Pt=l;else t:for(n=t;Pt!==null;){l=Pt;var o=l.sibling,u=l.return;if(Ih(l),l===n){Pt=null;break t}if(o!==null){o.return=u,Pt=o;break t}Pt=u}}}var o1={getCacheForType:function(t){var e=ne(Xt),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},u1=typeof WeakMap=="function"?WeakMap:Map,vt=0,Mt=null,ct=null,ht=0,xt=0,be=null,Vn=!1,Qa=!1,lu=!1,dn=0,zt=0,zn=0,fa=0,su=0,Ne=0,Pa=0,Ii=null,fe=null,ru=!1,ou=0,Vs=1/0,zs=null,_n=null,Wt=0,Un=null,$a=null,Ja=0,uu=0,cu=null,um=null,tl=0,fu=null;function Se(){if((vt&2)!==0&&ht!==0)return ht&-ht;if(U.T!==null){var t=Ba;return t!==0?t:vu()}return Mf()}function cm(){Ne===0&&(Ne=(ht&536870912)===0||gt?Sf():536870912);var t=Ce.current;return t!==null&&(t.flags|=32),Ne}function Te(t,e,n){(t===Mt&&(xt===2||xt===9)||t.cancelPendingCommit!==null)&&(Fa(t,0),Bn(t,ht,Ne,!1)),vi(t,n),((vt&2)===0||t!==Mt)&&(t===Mt&&((vt&2)===0&&(fa|=n),zt===4&&Bn(t,ht,Ne,!1)),Ke(t))}function fm(t,e,n){if((vt&6)!==0)throw Error(r(327));var l=!n&&(e&124)===0&&(e&t.expiredLanes)===0||gi(t,e),o=l?d1(t,e):mu(t,e,!0),u=l;do{if(o===0){Qa&&!l&&Bn(t,e,0,!1);break}else{if(n=t.current.alternate,u&&!c1(n)){o=mu(t,e,!1),u=!1;continue}if(o===2){if(u=e,t.errorRecoveryDisabledLanes&u)var y=0;else y=t.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){e=y;t:{var v=t;o=Ii;var T=v.current.memoizedState.isDehydrated;if(T&&(Fa(v,y).flags|=256),y=mu(v,y,!1),y!==2){if(lu&&!T){v.errorRecoveryDisabledLanes|=u,fa|=u,o=4;break t}u=fe,fe=o,u!==null&&(fe===null?fe=u:fe.push.apply(fe,u))}o=y}if(u=!1,o!==2)continue}}if(o===1){Fa(t,0),Bn(t,e,0,!0);break}t:{switch(l=t,u=o,u){case 0:case 1:throw Error(r(345));case 4:if((e&4194048)!==e)break;case 6:Bn(l,e,Ne,!Vn);break t;case 2:fe=null;break;case 3:case 5:break;default:throw Error(r(329))}if((e&62914560)===e&&(o=ou+300-ke(),10<o)){if(Bn(l,e,Ne,!Vn),Kl(l,0,!0)!==0)break t;l.timeoutHandle=km(dm.bind(null,l,n,fe,zs,ru,e,Ne,fa,Pa,Vn,u,2,-0,0),o);break t}dm(l,n,fe,zs,ru,e,Ne,fa,Pa,Vn,u,0,-0,0)}}break}while(!0);Ke(t)}function dm(t,e,n,l,o,u,y,v,T,N,_,H,j,O){if(t.timeoutHandle=-1,H=e.subtreeFlags,(H&8192||(H&16785408)===16785408)&&(rl={stylesheets:null,count:0,unsuspend:K1},lm(e),H=Q1(),H!==null)){t.cancelPendingCommit=H(xm.bind(null,t,e,u,n,l,o,y,v,T,_,1,j,O)),Bn(t,u,y,!N);return}xm(t,e,u,n,l,o,y,v,T)}function c1(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var o=n[l],u=o.getSnapshot;o=o.value;try{if(!ye(u(),o))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Bn(t,e,n,l){e&=~su,e&=~fa,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var o=e;0<o;){var u=31-pe(o),y=1<<u;l[u]=-1,o&=~y}n!==0&&Af(t,n,e)}function _s(){return(vt&6)===0?(el(0),!1):!0}function du(){if(ct!==null){if(xt===0)var t=ct.return;else t=ct,an=la=null,Co(t),Ga=null,Ki=0,t=ct;for(;t!==null;)Kh(t.alternate,t),t=t.return;ct=null}}function Fa(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,R1(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),du(),Mt=t,ct=n=tn(t.current,null),ht=e,xt=0,be=null,Vn=!1,Qa=gi(t,e),lu=!1,Pa=Ne=su=fa=zn=zt=0,fe=Ii=null,ru=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var o=31-pe(l),u=1<<o;e|=t[o],l&=~u}return dn=e,as(),n}function hm(t,e){ot=null,U.H=Ts,e===Ui||e===ds?(e=Nd(),xt=3):e===Dd?(e=Nd(),xt=4):xt=e===jh?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,be=e,ct===null&&(zt=1,Ds(t,Me(e,t.current)))}function mm(){var t=U.H;return U.H=Ts,t===null?Ts:t}function pm(){var t=U.A;return U.A=o1,t}function hu(){zt=4,Vn||(ht&4194048)!==ht&&Ce.current!==null||(Qa=!0),(zn&134217727)===0&&(fa&134217727)===0||Mt===null||Bn(Mt,ht,Ne,!1)}function mu(t,e,n){var l=vt;vt|=2;var o=mm(),u=pm();(Mt!==t||ht!==e)&&(zs=null,Fa(t,e)),e=!1;var y=zt;t:do try{if(xt!==0&&ct!==null){var v=ct,T=be;switch(xt){case 8:du(),y=6;break t;case 3:case 2:case 9:case 6:Ce.current===null&&(e=!0);var N=xt;if(xt=0,be=null,Wa(t,v,T,N),n&&Qa){y=0;break t}break;default:N=xt,xt=0,be=null,Wa(t,v,T,N)}}f1(),y=zt;break}catch(_){hm(t,_)}while(!0);return e&&t.shellSuspendCounter++,an=la=null,vt=l,U.H=o,U.A=u,ct===null&&(Mt=null,ht=0,as()),y}function f1(){for(;ct!==null;)ym(ct)}function d1(t,e){var n=vt;vt|=2;var l=mm(),o=pm();Mt!==t||ht!==e?(zs=null,Vs=ke()+500,Fa(t,e)):Qa=gi(t,e);t:do try{if(xt!==0&&ct!==null){e=ct;var u=be;e:switch(xt){case 1:xt=0,be=null,Wa(t,e,u,1);break;case 2:case 9:if(Rd(u)){xt=0,be=null,gm(e);break}e=function(){xt!==2&&xt!==9||Mt!==t||(xt=7),Ke(t)},u.then(e,e);break t;case 3:xt=7;break t;case 4:xt=5;break t;case 7:Rd(u)?(xt=0,be=null,gm(e)):(xt=0,be=null,Wa(t,e,u,7));break;case 5:var y=null;switch(ct.tag){case 26:y=ct.memoizedState;case 5:case 27:var v=ct;if(!y||Wm(y)){xt=0,be=null;var T=v.sibling;if(T!==null)ct=T;else{var N=v.return;N!==null?(ct=N,Us(N)):ct=null}break e}}xt=0,be=null,Wa(t,e,u,5);break;case 6:xt=0,be=null,Wa(t,e,u,6);break;case 8:du(),zt=6;break t;default:throw Error(r(462))}}h1();break}catch(_){hm(t,_)}while(!0);return an=la=null,U.H=l,U.A=o,vt=n,ct!==null?0:(Mt=null,ht=0,as(),zt)}function h1(){for(;ct!==null&&!_g();)ym(ct)}function ym(t){var e=Gh(t.alternate,t,dn);t.memoizedProps=t.pendingProps,e===null?Us(t):ct=e}function gm(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=Bh(n,e,e.pendingProps,e.type,void 0,ht);break;case 11:e=Bh(n,e,e.pendingProps,e.type.render,e.ref,ht);break;case 5:Co(e);default:Kh(n,e),e=ct=vd(e,dn),e=Gh(n,e,dn)}t.memoizedProps=t.pendingProps,e===null?Us(t):ct=e}function Wa(t,e,n,l){an=la=null,Co(e),Ga=null,Ki=0;var o=e.return;try{if(n1(t,o,e,n,ht)){zt=1,Ds(t,Me(n,t.current)),ct=null;return}}catch(u){if(o!==null)throw ct=o,u;zt=1,Ds(t,Me(n,t.current)),ct=null;return}e.flags&32768?(gt||l===1?t=!0:Qa||(ht&536870912)!==0?t=!1:(Vn=t=!0,(l===2||l===9||l===3||l===6)&&(l=Ce.current,l!==null&&l.tag===13&&(l.flags|=16384))),vm(e,t)):Us(e)}function Us(t){var e=t;do{if((e.flags&32768)!==0){vm(e,Vn);return}t=e.return;var n=i1(e.alternate,e,dn);if(n!==null){ct=n;return}if(e=e.sibling,e!==null){ct=e;return}ct=e=t}while(e!==null);zt===0&&(zt=5)}function vm(t,e){do{var n=l1(t.alternate,t);if(n!==null){n.flags&=32767,ct=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){ct=t;return}ct=t=n}while(t!==null);zt=6,ct=null}function xm(t,e,n,l,o,u,y,v,T){t.cancelPendingCommit=null;do Bs();while(Wt!==0);if((vt&6)!==0)throw Error(r(327));if(e!==null){if(e===t.current)throw Error(r(177));if(u=e.lanes|e.childLanes,u|=ao,Kg(t,n,u,y,v,T),t===Mt&&(ct=Mt=null,ht=0),$a=e,Un=t,Ja=n,uu=u,cu=o,um=l,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,g1(Yl,function(){return Em(),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||l){l=U.T,U.T=null,o=X.p,X.p=2,y=vt,vt|=4;try{s1(t,e,n)}finally{vt=y,X.p=o,U.T=l}}Wt=1,bm(),Sm(),Tm()}}function bm(){if(Wt===1){Wt=0;var t=Un,e=$a,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=U.T,U.T=null;var l=X.p;X.p=2;var o=vt;vt|=4;try{nm(e,t);var u=wu,y=od(t.containerInfo),v=u.focusedElem,T=u.selectionRange;if(y!==v&&v&&v.ownerDocument&&rd(v.ownerDocument.documentElement,v)){if(T!==null&&Wr(v)){var N=T.start,_=T.end;if(_===void 0&&(_=N),"selectionStart"in v)v.selectionStart=N,v.selectionEnd=Math.min(_,v.value.length);else{var H=v.ownerDocument||document,j=H&&H.defaultView||window;if(j.getSelection){var O=j.getSelection(),et=v.textContent.length,I=Math.min(T.start,et),At=T.end===void 0?I:Math.min(T.end,et);!O.extend&&I>At&&(y=At,At=I,I=y);var w=sd(v,I),E=sd(v,At);if(w&&E&&(O.rangeCount!==1||O.anchorNode!==w.node||O.anchorOffset!==w.offset||O.focusNode!==E.node||O.focusOffset!==E.offset)){var R=H.createRange();R.setStart(w.node,w.offset),O.removeAllRanges(),I>At?(O.addRange(R),O.extend(E.node,E.offset)):(R.setEnd(E.node,E.offset),O.addRange(R))}}}}for(H=[],O=v;O=O.parentNode;)O.nodeType===1&&H.push({element:O,left:O.scrollLeft,top:O.scrollTop});for(typeof v.focus=="function"&&v.focus(),v=0;v<H.length;v++){var L=H[v];L.element.scrollLeft=L.left,L.element.scrollTop=L.top}}$s=!!Mu,wu=Mu=null}finally{vt=o,X.p=l,U.T=n}}t.current=e,Wt=2}}function Sm(){if(Wt===2){Wt=0;var t=Un,e=$a,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=U.T,U.T=null;var l=X.p;X.p=2;var o=vt;vt|=4;try{Wh(t,e.alternate,e)}finally{vt=o,X.p=l,U.T=n}}Wt=3}}function Tm(){if(Wt===4||Wt===3){Wt=0,Ug();var t=Un,e=$a,n=Ja,l=um;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Wt=5:(Wt=0,$a=Un=null,Am(t,t.pendingLanes));var o=t.pendingLanes;if(o===0&&(_n=null),jr(n),e=e.stateNode,me&&typeof me.onCommitFiberRoot=="function")try{me.onCommitFiberRoot(yi,e,void 0,(e.current.flags&128)===128)}catch{}if(l!==null){e=U.T,o=X.p,X.p=2,U.T=null;try{for(var u=t.onRecoverableError,y=0;y<l.length;y++){var v=l[y];u(v.value,{componentStack:v.stack})}}finally{U.T=e,X.p=o}}(Ja&3)!==0&&Bs(),Ke(t),o=t.pendingLanes,(n&4194090)!==0&&(o&42)!==0?t===fu?tl++:(tl=0,fu=t):tl=0,el(0)}}function Am(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,zi(e)))}function Bs(t){return bm(),Sm(),Tm(),Em()}function Em(){if(Wt!==5)return!1;var t=Un,e=uu;uu=0;var n=jr(Ja),l=U.T,o=X.p;try{X.p=32>n?32:n,U.T=null,n=cu,cu=null;var u=Un,y=Ja;if(Wt=0,$a=Un=null,Ja=0,(vt&6)!==0)throw Error(r(331));var v=vt;if(vt|=4,rm(u.current),im(u,u.current,y,n),vt=v,el(0,!1),me&&typeof me.onPostCommitFiberRoot=="function")try{me.onPostCommitFiberRoot(yi,u)}catch{}return!0}finally{X.p=o,U.T=l,Am(t,e)}}function Mm(t,e,n){e=Me(n,e),e=Go(t.stateNode,e,2),t=wn(t,e,2),t!==null&&(vi(t,2),Ke(t))}function Et(t,e,n){if(t.tag===3)Mm(t,t,n);else for(;e!==null;){if(e.tag===3){Mm(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(_n===null||!_n.has(l))){t=Me(n,t),n=Ch(2),l=wn(e,n,2),l!==null&&(Nh(n,l,e,t),vi(l,2),Ke(l));break}}e=e.return}}function pu(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new u1;var o=new Set;l.set(e,o)}else o=l.get(e),o===void 0&&(o=new Set,l.set(e,o));o.has(n)||(lu=!0,o.add(n),t=m1.bind(null,t,e,n),e.then(t,t))}function m1(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,Mt===t&&(ht&n)===n&&(zt===4||zt===3&&(ht&62914560)===ht&&300>ke()-ou?(vt&2)===0&&Fa(t,0):su|=n,Pa===ht&&(Pa=0)),Ke(t)}function wm(t,e){e===0&&(e=Tf()),t=Va(t,e),t!==null&&(vi(t,e),Ke(t))}function p1(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),wm(t,n)}function y1(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,o=t.memoizedState;o!==null&&(n=o.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(r(314))}l!==null&&l.delete(e),wm(t,n)}function g1(t,e){return Dr(t,e)}var Ls=null,Ia=null,yu=!1,Hs=!1,gu=!1,da=0;function Ke(t){t!==Ia&&t.next===null&&(Ia===null?Ls=Ia=t:Ia=Ia.next=t),Hs=!0,yu||(yu=!0,x1())}function el(t,e){if(!gu&&Hs){gu=!0;do for(var n=!1,l=Ls;l!==null;){if(t!==0){var o=l.pendingLanes;if(o===0)var u=0;else{var y=l.suspendedLanes,v=l.pingedLanes;u=(1<<31-pe(42|t)+1)-1,u&=o&~(y&~v),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(n=!0,Nm(l,u))}else u=ht,u=Kl(l,l===Mt?u:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(u&3)===0||gi(l,u)||(n=!0,Nm(l,u));l=l.next}while(n);gu=!1}}function v1(){Dm()}function Dm(){Hs=yu=!1;var t=0;da!==0&&(D1()&&(t=da),da=0);for(var e=ke(),n=null,l=Ls;l!==null;){var o=l.next,u=Rm(l,e);u===0?(l.next=null,n===null?Ls=o:n.next=o,o===null&&(Ia=n)):(n=l,(t!==0||(u&3)!==0)&&(Hs=!0)),l=o}el(t)}function Rm(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,o=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var y=31-pe(u),v=1<<y,T=o[y];T===-1?((v&n)===0||(v&l)!==0)&&(o[y]=Xg(v,e)):T<=e&&(t.expiredLanes|=v),u&=~v}if(e=Mt,n=ht,n=Kl(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,n===0||t===e&&(xt===2||xt===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&Rr(l),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||gi(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&Rr(l),jr(n)){case 2:case 8:n=xf;break;case 32:n=Yl;break;case 268435456:n=bf;break;default:n=Yl}return l=Cm.bind(null,t),n=Dr(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&Rr(l),t.callbackPriority=2,t.callbackNode=null,2}function Cm(t,e){if(Wt!==0&&Wt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(Bs()&&t.callbackNode!==n)return null;var l=ht;return l=Kl(t,t===Mt?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(fm(t,l,e),Rm(t,ke()),t.callbackNode!=null&&t.callbackNode===n?Cm.bind(null,t):null)}function Nm(t,e){if(Bs())return null;fm(t,e,!0)}function x1(){C1(function(){(vt&6)!==0?Dr(vf,v1):Dm()})}function vu(){return da===0&&(da=Sf()),da}function jm(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Jl(""+t)}function Om(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function b1(t,e,n,l,o){if(e==="submit"&&n&&n.stateNode===o){var u=jm((o[re]||null).action),y=l.submitter;y&&(e=(e=y[re]||null)?jm(e.formAction):y.getAttribute("formAction"),e!==null&&(u=e,y=null));var v=new ts("action","action",null,l,o);t.push({event:v,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(da!==0){var T=y?Om(o,y):new FormData(o);Lo(n,{pending:!0,data:T,method:o.method,action:u},null,T)}}else typeof u=="function"&&(v.preventDefault(),T=y?Om(o,y):new FormData(o),Lo(n,{pending:!0,data:T,method:o.method,action:u},u,T))},currentTarget:o}]})}}for(var xu=0;xu<no.length;xu++){var bu=no[xu],S1=bu.toLowerCase(),T1=bu[0].toUpperCase()+bu.slice(1);_e(S1,"on"+T1)}_e(fd,"onAnimationEnd"),_e(dd,"onAnimationIteration"),_e(hd,"onAnimationStart"),_e("dblclick","onDoubleClick"),_e("focusin","onFocus"),_e("focusout","onBlur"),_e(Hv,"onTransitionRun"),_e(kv,"onTransitionStart"),_e(qv,"onTransitionCancel"),_e(md,"onTransitionEnd"),Aa("onMouseEnter",["mouseout","mouseover"]),Aa("onMouseLeave",["mouseout","mouseover"]),Aa("onPointerEnter",["pointerout","pointerover"]),Aa("onPointerLeave",["pointerout","pointerover"]),Jn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Jn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Jn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Jn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Jn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Jn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var nl="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),A1=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(nl));function Vm(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],o=l.event;l=l.listeners;t:{var u=void 0;if(e)for(var y=l.length-1;0<=y;y--){var v=l[y],T=v.instance,N=v.currentTarget;if(v=v.listener,T!==u&&o.isPropagationStopped())break t;u=v,o.currentTarget=N;try{u(o)}catch(_){ws(_)}o.currentTarget=null,u=T}else for(y=0;y<l.length;y++){if(v=l[y],T=v.instance,N=v.currentTarget,v=v.listener,T!==u&&o.isPropagationStopped())break t;u=v,o.currentTarget=N;try{u(o)}catch(_){ws(_)}o.currentTarget=null,u=T}}}}function ft(t,e){var n=e[Or];n===void 0&&(n=e[Or]=new Set);var l=t+"__bubble";n.has(l)||(zm(e,t,2,!1),n.add(l))}function Su(t,e,n){var l=0;e&&(l|=4),zm(n,t,l,e)}var ks="_reactListening"+Math.random().toString(36).slice(2);function Tu(t){if(!t[ks]){t[ks]=!0,Df.forEach(function(n){n!=="selectionchange"&&(A1.has(n)||Su(n,!1,t),Su(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[ks]||(e[ks]=!0,Su("selectionchange",!1,e))}}function zm(t,e,n,l){switch(ip(e)){case 2:var o=J1;break;case 8:o=F1;break;default:o=Uu}n=o.bind(null,e,n,t),o=void 0,!Gr||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(o=!0),l?o!==void 0?t.addEventListener(e,n,{capture:!0,passive:o}):t.addEventListener(e,n,!0):o!==void 0?t.addEventListener(e,n,{passive:o}):t.addEventListener(e,n,!1)}function Au(t,e,n,l,o){var u=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var y=l.tag;if(y===3||y===4){var v=l.stateNode.containerInfo;if(v===o)break;if(y===4)for(y=l.return;y!==null;){var T=y.tag;if((T===3||T===4)&&y.stateNode.containerInfo===o)return;y=y.return}for(;v!==null;){if(y=ba(v),y===null)return;if(T=y.tag,T===5||T===6||T===26||T===27){l=u=y;continue t}v=v.parentNode}}l=l.return}qf(function(){var N=u,_=qr(n),H=[];t:{var j=pd.get(t);if(j!==void 0){var O=ts,et=t;switch(t){case"keypress":if(Wl(n)===0)break t;case"keydown":case"keyup":O=gv;break;case"focusin":et="focus",O=Qr;break;case"focusout":et="blur",O=Qr;break;case"beforeblur":case"afterblur":O=Qr;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":O=Xf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":O=lv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":O=bv;break;case fd:case dd:case hd:O=ov;break;case md:O=Tv;break;case"scroll":case"scrollend":O=av;break;case"wheel":O=Ev;break;case"copy":case"cut":case"paste":O=cv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":O=Zf;break;case"toggle":case"beforetoggle":O=wv}var I=(e&4)!==0,At=!I&&(t==="scroll"||t==="scrollend"),w=I?j!==null?j+"Capture":null:j;I=[];for(var E=N,R;E!==null;){var L=E;if(R=L.stateNode,L=L.tag,L!==5&&L!==26&&L!==27||R===null||w===null||(L=Si(E,w),L!=null&&I.push(al(E,L,R))),At)break;E=E.return}0<I.length&&(j=new O(j,et,null,n,_),H.push({event:j,listeners:I}))}}if((e&7)===0){t:{if(j=t==="mouseover"||t==="pointerover",O=t==="mouseout"||t==="pointerout",j&&n!==kr&&(et=n.relatedTarget||n.fromElement)&&(ba(et)||et[xa]))break t;if((O||j)&&(j=_.window===_?_:(j=_.ownerDocument)?j.defaultView||j.parentWindow:window,O?(et=n.relatedTarget||n.toElement,O=N,et=et?ba(et):null,et!==null&&(At=f(et),I=et.tag,et!==At||I!==5&&I!==27&&I!==6)&&(et=null)):(O=null,et=N),O!==et)){if(I=Xf,L="onMouseLeave",w="onMouseEnter",E="mouse",(t==="pointerout"||t==="pointerover")&&(I=Zf,L="onPointerLeave",w="onPointerEnter",E="pointer"),At=O==null?j:bi(O),R=et==null?j:bi(et),j=new I(L,E+"leave",O,n,_),j.target=At,j.relatedTarget=R,L=null,ba(_)===N&&(I=new I(w,E+"enter",et,n,_),I.target=R,I.relatedTarget=At,L=I),At=L,O&&et)e:{for(I=O,w=et,E=0,R=I;R;R=ti(R))E++;for(R=0,L=w;L;L=ti(L))R++;for(;0<E-R;)I=ti(I),E--;for(;0<R-E;)w=ti(w),R--;for(;E--;){if(I===w||w!==null&&I===w.alternate)break e;I=ti(I),w=ti(w)}I=null}else I=null;O!==null&&_m(H,j,O,I,!1),et!==null&&At!==null&&_m(H,At,et,I,!0)}}t:{if(j=N?bi(N):window,O=j.nodeName&&j.nodeName.toLowerCase(),O==="select"||O==="input"&&j.type==="file")var Q=td;else if(Wf(j))if(ed)Q=Uv;else{Q=zv;var ut=Vv}else O=j.nodeName,!O||O.toLowerCase()!=="input"||j.type!=="checkbox"&&j.type!=="radio"?N&&Hr(N.elementType)&&(Q=td):Q=_v;if(Q&&(Q=Q(t,N))){If(H,Q,n,_);break t}ut&&ut(t,j,N),t==="focusout"&&N&&j.type==="number"&&N.memoizedProps.value!=null&&Lr(j,"number",j.value)}switch(ut=N?bi(N):window,t){case"focusin":(Wf(ut)||ut.contentEditable==="true")&&(Na=ut,Ir=N,Ci=null);break;case"focusout":Ci=Ir=Na=null;break;case"mousedown":to=!0;break;case"contextmenu":case"mouseup":case"dragend":to=!1,ud(H,n,_);break;case"selectionchange":if(Lv)break;case"keydown":case"keyup":ud(H,n,_)}var F;if($r)t:{switch(t){case"compositionstart":var tt="onCompositionStart";break t;case"compositionend":tt="onCompositionEnd";break t;case"compositionupdate":tt="onCompositionUpdate";break t}tt=void 0}else Ca?Jf(t,n)&&(tt="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(tt="onCompositionStart");tt&&(Qf&&n.locale!=="ko"&&(Ca||tt!=="onCompositionStart"?tt==="onCompositionEnd"&&Ca&&(F=Yf()):(Tn=_,Xr="value"in Tn?Tn.value:Tn.textContent,Ca=!0)),ut=qs(N,tt),0<ut.length&&(tt=new Kf(tt,t,null,n,_),H.push({event:tt,listeners:ut}),F?tt.data=F:(F=Ff(n),F!==null&&(tt.data=F)))),(F=Rv?Cv(t,n):Nv(t,n))&&(tt=qs(N,"onBeforeInput"),0<tt.length&&(ut=new Kf("onBeforeInput","beforeinput",null,n,_),H.push({event:ut,listeners:tt}),ut.data=F)),b1(H,t,N,n,_)}Vm(H,e)})}function al(t,e,n){return{instance:t,listener:e,currentTarget:n}}function qs(t,e){for(var n=e+"Capture",l=[];t!==null;){var o=t,u=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||u===null||(o=Si(t,n),o!=null&&l.unshift(al(t,o,u)),o=Si(t,e),o!=null&&l.push(al(t,o,u))),t.tag===3)return l;t=t.return}return[]}function ti(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function _m(t,e,n,l,o){for(var u=e._reactName,y=[];n!==null&&n!==l;){var v=n,T=v.alternate,N=v.stateNode;if(v=v.tag,T!==null&&T===l)break;v!==5&&v!==26&&v!==27||N===null||(T=N,o?(N=Si(n,u),N!=null&&y.unshift(al(n,N,T))):o||(N=Si(n,u),N!=null&&y.push(al(n,N,T)))),n=n.return}y.length!==0&&t.push({event:e,listeners:y})}var E1=/\r\n?/g,M1=/\u0000|\uFFFD/g;function Um(t){return(typeof t=="string"?t:""+t).replace(E1,`
`).replace(M1,"")}function Bm(t,e){return e=Um(e),Um(t)===e}function Ys(){}function Tt(t,e,n,l,o,u){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||wa(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&wa(t,""+l);break;case"className":Ql(t,"class",l);break;case"tabIndex":Ql(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Ql(t,n,l);break;case"style":Hf(t,l,u);break;case"data":if(e!=="object"){Ql(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=Jl(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(n==="formAction"?(e!=="input"&&Tt(t,e,"name",o.name,o,null),Tt(t,e,"formEncType",o.formEncType,o,null),Tt(t,e,"formMethod",o.formMethod,o,null),Tt(t,e,"formTarget",o.formTarget,o,null)):(Tt(t,e,"encType",o.encType,o,null),Tt(t,e,"method",o.method,o,null),Tt(t,e,"target",o.target,o,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=Jl(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=Ys);break;case"onScroll":l!=null&&ft("scroll",t);break;case"onScrollEnd":l!=null&&ft("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(n=l.__html,n!=null){if(o.children!=null)throw Error(r(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=Jl(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":ft("beforetoggle",t),ft("toggle",t),Zl(t,"popover",l);break;case"xlinkActuate":We(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":We(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":We(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":We(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":We(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":We(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":We(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":We(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":We(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Zl(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=ev.get(n)||n,Zl(t,n,l))}}function Eu(t,e,n,l,o,u){switch(n){case"style":Hf(t,l,u);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(n=l.__html,n!=null){if(o.children!=null)throw Error(r(60));t.innerHTML=n}}break;case"children":typeof l=="string"?wa(t,l):(typeof l=="number"||typeof l=="bigint")&&wa(t,""+l);break;case"onScroll":l!=null&&ft("scroll",t);break;case"onScrollEnd":l!=null&&ft("scrollend",t);break;case"onClick":l!=null&&(t.onclick=Ys);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Rf.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(o=n.endsWith("Capture"),e=n.slice(2,o?n.length-7:void 0),u=t[re]||null,u=u!=null?u[n]:null,typeof u=="function"&&t.removeEventListener(e,u,o),typeof l=="function")){typeof u!="function"&&u!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,o);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):Zl(t,n,l)}}}function It(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ft("error",t),ft("load",t);var l=!1,o=!1,u;for(u in n)if(n.hasOwnProperty(u)){var y=n[u];if(y!=null)switch(u){case"src":l=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:Tt(t,e,u,y,n,null)}}o&&Tt(t,e,"srcSet",n.srcSet,n,null),l&&Tt(t,e,"src",n.src,n,null);return;case"input":ft("invalid",t);var v=u=y=o=null,T=null,N=null;for(l in n)if(n.hasOwnProperty(l)){var _=n[l];if(_!=null)switch(l){case"name":o=_;break;case"type":y=_;break;case"checked":T=_;break;case"defaultChecked":N=_;break;case"value":u=_;break;case"defaultValue":v=_;break;case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(r(137,e));break;default:Tt(t,e,l,_,n,null)}}_f(t,u,v,T,N,y,o,!1),Pl(t);return;case"select":ft("invalid",t),l=y=u=null;for(o in n)if(n.hasOwnProperty(o)&&(v=n[o],v!=null))switch(o){case"value":u=v;break;case"defaultValue":y=v;break;case"multiple":l=v;default:Tt(t,e,o,v,n,null)}e=u,n=y,t.multiple=!!l,e!=null?Ma(t,!!l,e,!1):n!=null&&Ma(t,!!l,n,!0);return;case"textarea":ft("invalid",t),u=o=l=null;for(y in n)if(n.hasOwnProperty(y)&&(v=n[y],v!=null))switch(y){case"value":l=v;break;case"defaultValue":o=v;break;case"children":u=v;break;case"dangerouslySetInnerHTML":if(v!=null)throw Error(r(91));break;default:Tt(t,e,y,v,n,null)}Bf(t,l,o,u),Pl(t);return;case"option":for(T in n)if(n.hasOwnProperty(T)&&(l=n[T],l!=null))switch(T){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Tt(t,e,T,l,n,null)}return;case"dialog":ft("beforetoggle",t),ft("toggle",t),ft("cancel",t),ft("close",t);break;case"iframe":case"object":ft("load",t);break;case"video":case"audio":for(l=0;l<nl.length;l++)ft(nl[l],t);break;case"image":ft("error",t),ft("load",t);break;case"details":ft("toggle",t);break;case"embed":case"source":case"link":ft("error",t),ft("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(N in n)if(n.hasOwnProperty(N)&&(l=n[N],l!=null))switch(N){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:Tt(t,e,N,l,n,null)}return;default:if(Hr(e)){for(_ in n)n.hasOwnProperty(_)&&(l=n[_],l!==void 0&&Eu(t,e,_,l,n,void 0));return}}for(v in n)n.hasOwnProperty(v)&&(l=n[v],l!=null&&Tt(t,e,v,l,n,null))}function w1(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,u=null,y=null,v=null,T=null,N=null,_=null;for(O in n){var H=n[O];if(n.hasOwnProperty(O)&&H!=null)switch(O){case"checked":break;case"value":break;case"defaultValue":T=H;default:l.hasOwnProperty(O)||Tt(t,e,O,null,l,H)}}for(var j in l){var O=l[j];if(H=n[j],l.hasOwnProperty(j)&&(O!=null||H!=null))switch(j){case"type":u=O;break;case"name":o=O;break;case"checked":N=O;break;case"defaultChecked":_=O;break;case"value":y=O;break;case"defaultValue":v=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(r(137,e));break;default:O!==H&&Tt(t,e,j,O,l,H)}}Br(t,y,v,T,N,_,u,o);return;case"select":O=y=v=j=null;for(u in n)if(T=n[u],n.hasOwnProperty(u)&&T!=null)switch(u){case"value":break;case"multiple":O=T;default:l.hasOwnProperty(u)||Tt(t,e,u,null,l,T)}for(o in l)if(u=l[o],T=n[o],l.hasOwnProperty(o)&&(u!=null||T!=null))switch(o){case"value":j=u;break;case"defaultValue":v=u;break;case"multiple":y=u;default:u!==T&&Tt(t,e,o,u,l,T)}e=v,n=y,l=O,j!=null?Ma(t,!!n,j,!1):!!l!=!!n&&(e!=null?Ma(t,!!n,e,!0):Ma(t,!!n,n?[]:"",!1));return;case"textarea":O=j=null;for(v in n)if(o=n[v],n.hasOwnProperty(v)&&o!=null&&!l.hasOwnProperty(v))switch(v){case"value":break;case"children":break;default:Tt(t,e,v,null,l,o)}for(y in l)if(o=l[y],u=n[y],l.hasOwnProperty(y)&&(o!=null||u!=null))switch(y){case"value":j=o;break;case"defaultValue":O=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(r(91));break;default:o!==u&&Tt(t,e,y,o,l,u)}Uf(t,j,O);return;case"option":for(var et in n)if(j=n[et],n.hasOwnProperty(et)&&j!=null&&!l.hasOwnProperty(et))switch(et){case"selected":t.selected=!1;break;default:Tt(t,e,et,null,l,j)}for(T in l)if(j=l[T],O=n[T],l.hasOwnProperty(T)&&j!==O&&(j!=null||O!=null))switch(T){case"selected":t.selected=j&&typeof j!="function"&&typeof j!="symbol";break;default:Tt(t,e,T,j,l,O)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var I in n)j=n[I],n.hasOwnProperty(I)&&j!=null&&!l.hasOwnProperty(I)&&Tt(t,e,I,null,l,j);for(N in l)if(j=l[N],O=n[N],l.hasOwnProperty(N)&&j!==O&&(j!=null||O!=null))switch(N){case"children":case"dangerouslySetInnerHTML":if(j!=null)throw Error(r(137,e));break;default:Tt(t,e,N,j,l,O)}return;default:if(Hr(e)){for(var At in n)j=n[At],n.hasOwnProperty(At)&&j!==void 0&&!l.hasOwnProperty(At)&&Eu(t,e,At,void 0,l,j);for(_ in l)j=l[_],O=n[_],!l.hasOwnProperty(_)||j===O||j===void 0&&O===void 0||Eu(t,e,_,j,l,O);return}}for(var w in n)j=n[w],n.hasOwnProperty(w)&&j!=null&&!l.hasOwnProperty(w)&&Tt(t,e,w,null,l,j);for(H in l)j=l[H],O=n[H],!l.hasOwnProperty(H)||j===O||j==null&&O==null||Tt(t,e,H,j,l,O)}var Mu=null,wu=null;function Gs(t){return t.nodeType===9?t:t.ownerDocument}function Lm(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Hm(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Du(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Ru=null;function D1(){var t=window.event;return t&&t.type==="popstate"?t===Ru?!1:(Ru=t,!0):(Ru=null,!1)}var km=typeof setTimeout=="function"?setTimeout:void 0,R1=typeof clearTimeout=="function"?clearTimeout:void 0,qm=typeof Promise=="function"?Promise:void 0,C1=typeof queueMicrotask=="function"?queueMicrotask:typeof qm<"u"?function(t){return qm.resolve(null).then(t).catch(N1)}:km;function N1(t){setTimeout(function(){throw t})}function Ln(t){return t==="head"}function Ym(t,e){var n=e,l=0,o=0;do{var u=n.nextSibling;if(t.removeChild(n),u&&u.nodeType===8)if(n=u.data,n==="/$"){if(0<l&&8>l){n=l;var y=t.ownerDocument;if(n&1&&il(y.documentElement),n&2&&il(y.body),n&4)for(n=y.head,il(n),y=n.firstChild;y;){var v=y.nextSibling,T=y.nodeName;y[xi]||T==="SCRIPT"||T==="STYLE"||T==="LINK"&&y.rel.toLowerCase()==="stylesheet"||n.removeChild(y),y=v}}if(o===0){t.removeChild(u),dl(e);return}o--}else n==="$"||n==="$?"||n==="$!"?o++:l=n.charCodeAt(0)-48;else l=0;n=u}while(n);dl(e)}function Cu(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Cu(n),Vr(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function j1(t,e,n,l){for(;t.nodeType===1;){var o=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[xi])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==o.rel||t.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||t.getAttribute("title")!==(o.title==null?null:o.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(o.src==null?null:o.src)||t.getAttribute("type")!==(o.type==null?null:o.type)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=o.name==null?null:""+o.name;if(o.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=Be(t.nextSibling),t===null)break}return null}function O1(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Be(t.nextSibling),t===null))return null;return t}function Nu(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function V1(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var l=function(){e(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function Be(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var ju=null;function Gm(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function Xm(t,e,n){switch(e=Gs(n),t){case"html":if(t=e.documentElement,!t)throw Error(r(452));return t;case"head":if(t=e.head,!t)throw Error(r(453));return t;case"body":if(t=e.body,!t)throw Error(r(454));return t;default:throw Error(r(451))}}function il(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Vr(t)}var je=new Map,Km=new Set;function Xs(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var hn=X.d;X.d={f:z1,r:_1,D:U1,C:B1,L:L1,m:H1,X:q1,S:k1,M:Y1};function z1(){var t=hn.f(),e=_s();return t||e}function _1(t){var e=Sa(t);e!==null&&e.tag===5&&e.type==="form"?fh(e):hn.r(t)}var ei=typeof document>"u"?null:document;function Zm(t,e,n){var l=ei;if(l&&typeof e=="string"&&e){var o=Ee(e);o='link[rel="'+t+'"][href="'+o+'"]',typeof n=="string"&&(o+='[crossorigin="'+n+'"]'),Km.has(o)||(Km.add(o),t={rel:t,crossOrigin:n,href:e},l.querySelector(o)===null&&(e=l.createElement("link"),It(e,"link",t),Zt(e),l.head.appendChild(e)))}}function U1(t){hn.D(t),Zm("dns-prefetch",t,null)}function B1(t,e){hn.C(t,e),Zm("preconnect",t,e)}function L1(t,e,n){hn.L(t,e,n);var l=ei;if(l&&t&&e){var o='link[rel="preload"][as="'+Ee(e)+'"]';e==="image"&&n&&n.imageSrcSet?(o+='[imagesrcset="'+Ee(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(o+='[imagesizes="'+Ee(n.imageSizes)+'"]')):o+='[href="'+Ee(t)+'"]';var u=o;switch(e){case"style":u=ni(t);break;case"script":u=ai(t)}je.has(u)||(t=g({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),je.set(u,t),l.querySelector(o)!==null||e==="style"&&l.querySelector(ll(u))||e==="script"&&l.querySelector(sl(u))||(e=l.createElement("link"),It(e,"link",t),Zt(e),l.head.appendChild(e)))}}function H1(t,e){hn.m(t,e);var n=ei;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",o='link[rel="modulepreload"][as="'+Ee(l)+'"][href="'+Ee(t)+'"]',u=o;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=ai(t)}if(!je.has(u)&&(t=g({rel:"modulepreload",href:t},e),je.set(u,t),n.querySelector(o)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(sl(u)))return}l=n.createElement("link"),It(l,"link",t),Zt(l),n.head.appendChild(l)}}}function k1(t,e,n){hn.S(t,e,n);var l=ei;if(l&&t){var o=Ta(l).hoistableStyles,u=ni(t);e=e||"default";var y=o.get(u);if(!y){var v={loading:0,preload:null};if(y=l.querySelector(ll(u)))v.loading=5;else{t=g({rel:"stylesheet",href:t,"data-precedence":e},n),(n=je.get(u))&&Ou(t,n);var T=y=l.createElement("link");Zt(T),It(T,"link",t),T._p=new Promise(function(N,_){T.onload=N,T.onerror=_}),T.addEventListener("load",function(){v.loading|=1}),T.addEventListener("error",function(){v.loading|=2}),v.loading|=4,Ks(y,e,l)}y={type:"stylesheet",instance:y,count:1,state:v},o.set(u,y)}}}function q1(t,e){hn.X(t,e);var n=ei;if(n&&t){var l=Ta(n).hoistableScripts,o=ai(t),u=l.get(o);u||(u=n.querySelector(sl(o)),u||(t=g({src:t,async:!0},e),(e=je.get(o))&&Vu(t,e),u=n.createElement("script"),Zt(u),It(u,"link",t),n.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(o,u))}}function Y1(t,e){hn.M(t,e);var n=ei;if(n&&t){var l=Ta(n).hoistableScripts,o=ai(t),u=l.get(o);u||(u=n.querySelector(sl(o)),u||(t=g({src:t,async:!0,type:"module"},e),(e=je.get(o))&&Vu(t,e),u=n.createElement("script"),Zt(u),It(u,"link",t),n.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(o,u))}}function Qm(t,e,n,l){var o=(o=it.current)?Xs(o):null;if(!o)throw Error(r(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=ni(n.href),n=Ta(o).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=ni(n.href);var u=Ta(o).hoistableStyles,y=u.get(t);if(y||(o=o.ownerDocument||o,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,y),(u=o.querySelector(ll(t)))&&!u._p&&(y.instance=u,y.state.loading=5),je.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},je.set(t,n),u||G1(o,t,n,y.state))),e&&l===null)throw Error(r(528,""));return y}if(e&&l!==null)throw Error(r(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=ai(n),n=Ta(o).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,t))}}function ni(t){return'href="'+Ee(t)+'"'}function ll(t){return'link[rel="stylesheet"]['+t+"]"}function Pm(t){return g({},t,{"data-precedence":t.precedence,precedence:null})}function G1(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),It(e,"link",n),Zt(e),t.head.appendChild(e))}function ai(t){return'[src="'+Ee(t)+'"]'}function sl(t){return"script[async]"+t}function $m(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+Ee(n.href)+'"]');if(l)return e.instance=l,Zt(l),l;var o=g({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),Zt(l),It(l,"style",o),Ks(l,n.precedence,t),e.instance=l;case"stylesheet":o=ni(n.href);var u=t.querySelector(ll(o));if(u)return e.state.loading|=4,e.instance=u,Zt(u),u;l=Pm(n),(o=je.get(o))&&Ou(l,o),u=(t.ownerDocument||t).createElement("link"),Zt(u);var y=u;return y._p=new Promise(function(v,T){y.onload=v,y.onerror=T}),It(u,"link",l),e.state.loading|=4,Ks(u,n.precedence,t),e.instance=u;case"script":return u=ai(n.src),(o=t.querySelector(sl(u)))?(e.instance=o,Zt(o),o):(l=n,(o=je.get(u))&&(l=g({},n),Vu(l,o)),t=t.ownerDocument||t,o=t.createElement("script"),Zt(o),It(o,"link",l),t.head.appendChild(o),e.instance=o);case"void":return null;default:throw Error(r(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,Ks(l,n.precedence,t));return e.instance}function Ks(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=l.length?l[l.length-1]:null,u=o,y=0;y<l.length;y++){var v=l[y];if(v.dataset.precedence===e)u=v;else if(u!==o)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function Ou(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Vu(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Zs=null;function Jm(t,e,n){if(Zs===null){var l=new Map,o=Zs=new Map;o.set(n,l)}else o=Zs,l=o.get(n),l||(l=new Map,o.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),o=0;o<n.length;o++){var u=n[o];if(!(u[xi]||u[ee]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var y=u.getAttribute(e)||"";y=t+y;var v=l.get(y);v?v.push(u):l.set(y,[u])}}return l}function Fm(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function X1(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Wm(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var rl=null;function K1(){}function Z1(t,e,n){if(rl===null)throw Error(r(475));var l=rl;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var o=ni(n.href),u=t.querySelector(ll(o));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=Qs.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=u,Zt(u);return}u=t.ownerDocument||t,n=Pm(n),(o=je.get(o))&&Ou(n,o),u=u.createElement("link"),Zt(u);var y=u;y._p=new Promise(function(v,T){y.onload=v,y.onerror=T}),It(u,"link",n),e.instance=u}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=Qs.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function Q1(){if(rl===null)throw Error(r(475));var t=rl;return t.stylesheets&&t.count===0&&zu(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&zu(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function Qs(){if(this.count--,this.count===0){if(this.stylesheets)zu(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Ps=null;function zu(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Ps=new Map,e.forEach(P1,t),Ps=null,Qs.call(t))}function P1(t,e){if(!(e.state.loading&4)){var n=Ps.get(t);if(n)var l=n.get(null);else{n=new Map,Ps.set(t,n);for(var o=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<o.length;u++){var y=o[u];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(n.set(y.dataset.precedence,y),l=y)}l&&n.set(null,l)}o=e.instance,y=o.getAttribute("data-precedence"),u=n.get(y)||l,u===l&&n.set(null,o),n.set(y,o),this.count++,l=Qs.bind(this),o.addEventListener("load",l),o.addEventListener("error",l),u?u.parentNode.insertBefore(o,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(o,t.firstChild)),e.state.loading|=4}}var ol={$$typeof:k,Provider:null,Consumer:null,_currentValue:$,_currentValue2:$,_threadCount:0};function $1(t,e,n,l,o,u,y,v){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Cr(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Cr(0),this.hiddenUpdates=Cr(null),this.identifierPrefix=l,this.onUncaughtError=o,this.onCaughtError=u,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=v,this.incompleteTransitions=new Map}function Im(t,e,n,l,o,u,y,v,T,N,_,H){return t=new $1(t,e,n,y,v,T,N,H),e=1,u===!0&&(e|=24),u=ge(3,null,null,e),t.current=u,u.stateNode=t,e=po(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:l,isDehydrated:n,cache:e},xo(u),t}function tp(t){return t?(t=za,t):za}function ep(t,e,n,l,o,u){o=tp(o),l.context===null?l.context=o:l.pendingContext=o,l=Mn(e),l.payload={element:n},u=u===void 0?null:u,u!==null&&(l.callback=u),n=wn(t,l,e),n!==null&&(Te(n,t,e),Li(n,t,e))}function np(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function _u(t,e){np(t,e),(t=t.alternate)&&np(t,e)}function ap(t){if(t.tag===13){var e=Va(t,67108864);e!==null&&Te(e,t,67108864),_u(t,67108864)}}var $s=!0;function J1(t,e,n,l){var o=U.T;U.T=null;var u=X.p;try{X.p=2,Uu(t,e,n,l)}finally{X.p=u,U.T=o}}function F1(t,e,n,l){var o=U.T;U.T=null;var u=X.p;try{X.p=8,Uu(t,e,n,l)}finally{X.p=u,U.T=o}}function Uu(t,e,n,l){if($s){var o=Bu(l);if(o===null)Au(t,e,l,Js,n),lp(t,l);else if(I1(o,t,e,n,l))l.stopPropagation();else if(lp(t,l),e&4&&-1<W1.indexOf(t)){for(;o!==null;){var u=Sa(o);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var y=$n(u.pendingLanes);if(y!==0){var v=u;for(v.pendingLanes|=2,v.entangledLanes|=2;y;){var T=1<<31-pe(y);v.entanglements[1]|=T,y&=~T}Ke(u),(vt&6)===0&&(Vs=ke()+500,el(0))}}break;case 13:v=Va(u,2),v!==null&&Te(v,u,2),_s(),_u(u,2)}if(u=Bu(l),u===null&&Au(t,e,l,Js,n),u===o)break;o=u}o!==null&&l.stopPropagation()}else Au(t,e,l,null,n)}}function Bu(t){return t=qr(t),Lu(t)}var Js=null;function Lu(t){if(Js=null,t=ba(t),t!==null){var e=f(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=d(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Js=t,null}function ip(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Bg()){case vf:return 2;case xf:return 8;case Yl:case Lg:return 32;case bf:return 268435456;default:return 32}default:return 32}}var Hu=!1,Hn=null,kn=null,qn=null,ul=new Map,cl=new Map,Yn=[],W1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function lp(t,e){switch(t){case"focusin":case"focusout":Hn=null;break;case"dragenter":case"dragleave":kn=null;break;case"mouseover":case"mouseout":qn=null;break;case"pointerover":case"pointerout":ul.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":cl.delete(e.pointerId)}}function fl(t,e,n,l,o,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:u,targetContainers:[o]},e!==null&&(e=Sa(e),e!==null&&ap(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,o!==null&&e.indexOf(o)===-1&&e.push(o),t)}function I1(t,e,n,l,o){switch(e){case"focusin":return Hn=fl(Hn,t,e,n,l,o),!0;case"dragenter":return kn=fl(kn,t,e,n,l,o),!0;case"mouseover":return qn=fl(qn,t,e,n,l,o),!0;case"pointerover":var u=o.pointerId;return ul.set(u,fl(ul.get(u)||null,t,e,n,l,o)),!0;case"gotpointercapture":return u=o.pointerId,cl.set(u,fl(cl.get(u)||null,t,e,n,l,o)),!0}return!1}function sp(t){var e=ba(t.target);if(e!==null){var n=f(e);if(n!==null){if(e=n.tag,e===13){if(e=d(n),e!==null){t.blockedOn=e,Zg(t.priority,function(){if(n.tag===13){var l=Se();l=Nr(l);var o=Va(n,l);o!==null&&Te(o,n,l),_u(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Fs(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=Bu(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);kr=l,n.target.dispatchEvent(l),kr=null}else return e=Sa(n),e!==null&&ap(e),t.blockedOn=n,!1;e.shift()}return!0}function rp(t,e,n){Fs(t)&&n.delete(e)}function tx(){Hu=!1,Hn!==null&&Fs(Hn)&&(Hn=null),kn!==null&&Fs(kn)&&(kn=null),qn!==null&&Fs(qn)&&(qn=null),ul.forEach(rp),cl.forEach(rp)}function Ws(t,e){t.blockedOn===e&&(t.blockedOn=null,Hu||(Hu=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,tx)))}var Is=null;function op(t){Is!==t&&(Is=t,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){Is===t&&(Is=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],o=t[e+2];if(typeof l!="function"){if(Lu(l||n)===null)continue;break}var u=Sa(n);u!==null&&(t.splice(e,3),e-=3,Lo(u,{pending:!0,data:o,method:n.method,action:l},l,o))}}))}function dl(t){function e(T){return Ws(T,t)}Hn!==null&&Ws(Hn,t),kn!==null&&Ws(kn,t),qn!==null&&Ws(qn,t),ul.forEach(e),cl.forEach(e);for(var n=0;n<Yn.length;n++){var l=Yn[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<Yn.length&&(n=Yn[0],n.blockedOn===null);)sp(n),n.blockedOn===null&&Yn.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var o=n[l],u=n[l+1],y=o[re]||null;if(typeof u=="function")y||op(n);else if(y){var v=null;if(u&&u.hasAttribute("formAction")){if(o=u,y=u[re]||null)v=y.formAction;else if(Lu(o)!==null)continue}else v=y.action;typeof v=="function"?n[l+1]=v:(n.splice(l,3),l-=3),op(n)}}}function ku(t){this._internalRoot=t}tr.prototype.render=ku.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(r(409));var n=e.current,l=Se();ep(n,l,t,e,null,null)},tr.prototype.unmount=ku.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;ep(t.current,2,null,t,null,null),_s(),e[xa]=null}};function tr(t){this._internalRoot=t}tr.prototype.unstable_scheduleHydration=function(t){if(t){var e=Mf();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Yn.length&&e!==0&&e<Yn[n].priority;n++);Yn.splice(n,0,t),n===0&&sp(t)}};var up=i.version;if(up!=="19.1.0")throw Error(r(527,up,"19.1.0"));X.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(r(188)):(t=Object.keys(t).join(","),Error(r(268,t)));return t=m(e),t=t!==null?h(t):null,t=t===null?null:t.stateNode,t};var ex={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:U,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var er=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!er.isDisabled&&er.supportsFiber)try{yi=er.inject(ex),me=er}catch{}}return ml.createRoot=function(t,e){if(!c(t))throw Error(r(299));var n=!1,l="",o=Mh,u=wh,y=Dh,v=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(o=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(y=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(v=e.unstable_transitionCallbacks)),e=Im(t,1,!1,null,null,n,l,o,u,y,v,null),t[xa]=e.current,Tu(t),new ku(e)},ml.hydrateRoot=function(t,e,n){if(!c(t))throw Error(r(299));var l=!1,o="",u=Mh,y=wh,v=Dh,T=null,N=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onUncaughtError!==void 0&&(u=n.onUncaughtError),n.onCaughtError!==void 0&&(y=n.onCaughtError),n.onRecoverableError!==void 0&&(v=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(T=n.unstable_transitionCallbacks),n.formState!==void 0&&(N=n.formState)),e=Im(t,1,!0,e,n??null,l,o,u,y,v,T,N),e.context=tp(null),n=e.current,l=Se(),l=Nr(l),o=Mn(l),o.callback=null,wn(n,o,l),n=l,e.current.lanes=n,vi(e,n),Ke(e),t[xa]=e.current,Tu(t),new tr(e)},ml.version="19.1.0",ml}var xp;function fx(){if(xp)return Gu.exports;xp=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(i){console.error(i)}}return a(),Gu.exports=cx(),Gu.exports}var dx=fx(),pl={},bp;function hx(){if(bp)return pl;bp=1,Object.defineProperty(pl,"__esModule",{value:!0}),pl.parse=d,pl.serialize=h;const a=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,i=/^[\u0021-\u003A\u003C-\u007E]*$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,r=/^[\u0020-\u003A\u003D-\u007E]*$/,c=Object.prototype.toString,f=(()=>{const b=function(){};return b.prototype=Object.create(null),b})();function d(b,M){const D=new f,z=b.length;if(z<2)return D;const B=(M==null?void 0:M.decode)||g;let V=0;do{const q=b.indexOf("=",V);if(q===-1)break;const k=b.indexOf(";",V),P=k===-1?z:k;if(q>P){V=b.lastIndexOf(";",q-1)+1;continue}const G=p(b,V,q),at=m(b,q,G),lt=b.slice(G,at);if(D[lt]===void 0){let J=p(b,q+1,P),rt=m(b,P,J);const bt=B(b.slice(J,rt));D[lt]=bt}V=P+1}while(V<z);return D}function p(b,M,D){do{const z=b.charCodeAt(M);if(z!==32&&z!==9)return M}while(++M<D);return D}function m(b,M,D){for(;M>D;){const z=b.charCodeAt(--M);if(z!==32&&z!==9)return M+1}return D}function h(b,M,D){const z=(D==null?void 0:D.encode)||encodeURIComponent;if(!a.test(b))throw new TypeError(`argument name is invalid: ${b}`);const B=z(M);if(!i.test(B))throw new TypeError(`argument val is invalid: ${M}`);let V=b+"="+B;if(!D)return V;if(D.maxAge!==void 0){if(!Number.isInteger(D.maxAge))throw new TypeError(`option maxAge is invalid: ${D.maxAge}`);V+="; Max-Age="+D.maxAge}if(D.domain){if(!s.test(D.domain))throw new TypeError(`option domain is invalid: ${D.domain}`);V+="; Domain="+D.domain}if(D.path){if(!r.test(D.path))throw new TypeError(`option path is invalid: ${D.path}`);V+="; Path="+D.path}if(D.expires){if(!x(D.expires)||!Number.isFinite(D.expires.valueOf()))throw new TypeError(`option expires is invalid: ${D.expires}`);V+="; Expires="+D.expires.toUTCString()}if(D.httpOnly&&(V+="; HttpOnly"),D.secure&&(V+="; Secure"),D.partitioned&&(V+="; Partitioned"),D.priority)switch(typeof D.priority=="string"?D.priority.toLowerCase():void 0){case"low":V+="; Priority=Low";break;case"medium":V+="; Priority=Medium";break;case"high":V+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${D.priority}`)}if(D.sameSite)switch(typeof D.sameSite=="string"?D.sameSite.toLowerCase():D.sameSite){case!0:case"strict":V+="; SameSite=Strict";break;case"lax":V+="; SameSite=Lax";break;case"none":V+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${D.sameSite}`)}return V}function g(b){if(b.indexOf("%")===-1)return b;try{return decodeURIComponent(b)}catch{return b}}function x(b){return c.call(b)==="[object Date]"}return pl}hx();var Sp="popstate";function mx(a={}){function i(r,c){let{pathname:f,search:d,hash:p}=r.location;return oc("",{pathname:f,search:d,hash:p},c.state&&c.state.usr||null,c.state&&c.state.key||"default")}function s(r,c){return typeof c=="string"?c:wl(c)}return yx(i,s,null,a)}function jt(a,i){if(a===!1||a===null||typeof a>"u")throw new Error(i)}function $e(a,i){if(!a){typeof console<"u"&&console.warn(i);try{throw new Error(i)}catch{}}}function px(){return Math.random().toString(36).substring(2,10)}function Tp(a,i){return{usr:a.state,key:a.key,idx:i}}function oc(a,i,s=null,r){return{pathname:typeof a=="string"?a:a.pathname,search:"",hash:"",...typeof i=="string"?fi(i):i,state:s,key:i&&i.key||r||px()}}function wl({pathname:a="/",search:i="",hash:s=""}){return i&&i!=="?"&&(a+=i.charAt(0)==="?"?i:"?"+i),s&&s!=="#"&&(a+=s.charAt(0)==="#"?s:"#"+s),a}function fi(a){let i={};if(a){let s=a.indexOf("#");s>=0&&(i.hash=a.substring(s),a=a.substring(0,s));let r=a.indexOf("?");r>=0&&(i.search=a.substring(r),a=a.substring(0,r)),a&&(i.pathname=a)}return i}function yx(a,i,s,r={}){let{window:c=document.defaultView,v5Compat:f=!1}=r,d=c.history,p="POP",m=null,h=g();h==null&&(h=0,d.replaceState({...d.state,idx:h},""));function g(){return(d.state||{idx:null}).idx}function x(){p="POP";let B=g(),V=B==null?null:B-h;h=B,m&&m({action:p,location:z.location,delta:V})}function b(B,V){p="PUSH";let q=oc(z.location,B,V);h=g()+1;let k=Tp(q,h),P=z.createHref(q);try{d.pushState(k,"",P)}catch(G){if(G instanceof DOMException&&G.name==="DataCloneError")throw G;c.location.assign(P)}f&&m&&m({action:p,location:z.location,delta:1})}function M(B,V){p="REPLACE";let q=oc(z.location,B,V);h=g();let k=Tp(q,h),P=z.createHref(q);d.replaceState(k,"",P),f&&m&&m({action:p,location:z.location,delta:0})}function D(B){return gx(B)}let z={get action(){return p},get location(){return a(c,d)},listen(B){if(m)throw new Error("A history only accepts one active listener");return c.addEventListener(Sp,x),m=B,()=>{c.removeEventListener(Sp,x),m=null}},createHref(B){return i(c,B)},createURL:D,encodeLocation(B){let V=D(B);return{pathname:V.pathname,search:V.search,hash:V.hash}},push:b,replace:M,go(B){return d.go(B)}};return z}function gx(a,i=!1){let s="http://localhost";typeof window<"u"&&(s=window.location.origin!=="null"?window.location.origin:window.location.href),jt(s,"No window.location.(origin|href) available to create URL");let r=typeof a=="string"?a:wl(a);return r=r.replace(/ $/,"%20"),!i&&r.startsWith("//")&&(r=s+r),new URL(r,s)}function qy(a,i,s="/"){return vx(a,i,s,!1)}function vx(a,i,s,r){let c=typeof i=="string"?fi(i):i,f=pn(c.pathname||"/",s);if(f==null)return null;let d=Yy(a);xx(d);let p=null;for(let m=0;p==null&&m<d.length;++m){let h=Nx(f);p=Rx(d[m],h,r)}return p}function Yy(a,i=[],s=[],r=""){let c=(f,d,p)=>{let m={relativePath:p===void 0?f.path||"":p,caseSensitive:f.caseSensitive===!0,childrenIndex:d,route:f};m.relativePath.startsWith("/")&&(jt(m.relativePath.startsWith(r),`Absolute route path "${m.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),m.relativePath=m.relativePath.slice(r.length));let h=mn([r,m.relativePath]),g=s.concat(m);f.children&&f.children.length>0&&(jt(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${h}".`),Yy(f.children,i,g,h)),!(f.path==null&&!f.index)&&i.push({path:h,score:wx(h,f.index),routesMeta:g})};return a.forEach((f,d)=>{var p;if(f.path===""||!((p=f.path)!=null&&p.includes("?")))c(f,d);else for(let m of Gy(f.path))c(f,d,m)}),i}function Gy(a){let i=a.split("/");if(i.length===0)return[];let[s,...r]=i,c=s.endsWith("?"),f=s.replace(/\?$/,"");if(r.length===0)return c?[f,""]:[f];let d=Gy(r.join("/")),p=[];return p.push(...d.map(m=>m===""?f:[f,m].join("/"))),c&&p.push(...d),p.map(m=>a.startsWith("/")&&m===""?"/":m)}function xx(a){a.sort((i,s)=>i.score!==s.score?s.score-i.score:Dx(i.routesMeta.map(r=>r.childrenIndex),s.routesMeta.map(r=>r.childrenIndex)))}var bx=/^:[\w-]+$/,Sx=3,Tx=2,Ax=1,Ex=10,Mx=-2,Ap=a=>a==="*";function wx(a,i){let s=a.split("/"),r=s.length;return s.some(Ap)&&(r+=Mx),i&&(r+=Tx),s.filter(c=>!Ap(c)).reduce((c,f)=>c+(bx.test(f)?Sx:f===""?Ax:Ex),r)}function Dx(a,i){return a.length===i.length&&a.slice(0,-1).every((r,c)=>r===i[c])?a[a.length-1]-i[i.length-1]:0}function Rx(a,i,s=!1){let{routesMeta:r}=a,c={},f="/",d=[];for(let p=0;p<r.length;++p){let m=r[p],h=p===r.length-1,g=f==="/"?i:i.slice(f.length)||"/",x=dr({path:m.relativePath,caseSensitive:m.caseSensitive,end:h},g),b=m.route;if(!x&&h&&s&&!r[r.length-1].route.index&&(x=dr({path:m.relativePath,caseSensitive:m.caseSensitive,end:!1},g)),!x)return null;Object.assign(c,x.params),d.push({params:c,pathname:mn([f,x.pathname]),pathnameBase:zx(mn([f,x.pathnameBase])),route:b}),x.pathnameBase!=="/"&&(f=mn([f,x.pathnameBase]))}return d}function dr(a,i){typeof a=="string"&&(a={path:a,caseSensitive:!1,end:!0});let[s,r]=Cx(a.path,a.caseSensitive,a.end),c=i.match(s);if(!c)return null;let f=c[0],d=f.replace(/(.)\/+$/,"$1"),p=c.slice(1);return{params:r.reduce((h,{paramName:g,isOptional:x},b)=>{if(g==="*"){let D=p[b]||"";d=f.slice(0,f.length-D.length).replace(/(.)\/+$/,"$1")}const M=p[b];return x&&!M?h[g]=void 0:h[g]=(M||"").replace(/%2F/g,"/"),h},{}),pathname:f,pathnameBase:d,pattern:a}}function Cx(a,i=!1,s=!0){$e(a==="*"||!a.endsWith("*")||a.endsWith("/*"),`Route path "${a}" will be treated as if it were "${a.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${a.replace(/\*$/,"/*")}".`);let r=[],c="^"+a.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,p,m)=>(r.push({paramName:p,isOptional:m!=null}),m?"/?([^\\/]+)?":"/([^\\/]+)"));return a.endsWith("*")?(r.push({paramName:"*"}),c+=a==="*"||a==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?c+="\\/*$":a!==""&&a!=="/"&&(c+="(?:(?=\\/|$))"),[new RegExp(c,i?void 0:"i"),r]}function Nx(a){try{return a.split("/").map(i=>decodeURIComponent(i).replace(/\//g,"%2F")).join("/")}catch(i){return $e(!1,`The URL path "${a}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${i}).`),a}}function pn(a,i){if(i==="/")return a;if(!a.toLowerCase().startsWith(i.toLowerCase()))return null;let s=i.endsWith("/")?i.length-1:i.length,r=a.charAt(s);return r&&r!=="/"?null:a.slice(s)||"/"}function jx(a,i="/"){let{pathname:s,search:r="",hash:c=""}=typeof a=="string"?fi(a):a;return{pathname:s?s.startsWith("/")?s:Ox(s,i):i,search:_x(r),hash:Ux(c)}}function Ox(a,i){let s=i.replace(/\/+$/,"").split("/");return a.split("/").forEach(c=>{c===".."?s.length>1&&s.pop():c!=="."&&s.push(c)}),s.length>1?s.join("/"):"/"}function Qu(a,i,s,r){return`Cannot include a '${a}' character in a manually specified \`to.${i}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${s}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Vx(a){return a.filter((i,s)=>s===0||i.route.path&&i.route.path.length>0)}function Xy(a){let i=Vx(a);return i.map((s,r)=>r===i.length-1?s.pathname:s.pathnameBase)}function Ky(a,i,s,r=!1){let c;typeof a=="string"?c=fi(a):(c={...a},jt(!c.pathname||!c.pathname.includes("?"),Qu("?","pathname","search",c)),jt(!c.pathname||!c.pathname.includes("#"),Qu("#","pathname","hash",c)),jt(!c.search||!c.search.includes("#"),Qu("#","search","hash",c)));let f=a===""||c.pathname==="",d=f?"/":c.pathname,p;if(d==null)p=s;else{let x=i.length-1;if(!r&&d.startsWith("..")){let b=d.split("/");for(;b[0]==="..";)b.shift(),x-=1;c.pathname=b.join("/")}p=x>=0?i[x]:"/"}let m=jx(c,p),h=d&&d!=="/"&&d.endsWith("/"),g=(f||d===".")&&s.endsWith("/");return!m.pathname.endsWith("/")&&(h||g)&&(m.pathname+="/"),m}var mn=a=>a.join("/").replace(/\/\/+/g,"/"),zx=a=>a.replace(/\/+$/,"").replace(/^\/*/,"/"),_x=a=>!a||a==="?"?"":a.startsWith("?")?a:"?"+a,Ux=a=>!a||a==="#"?"":a.startsWith("#")?a:"#"+a;function Bx(a){return a!=null&&typeof a.status=="number"&&typeof a.statusText=="string"&&typeof a.internal=="boolean"&&"data"in a}var Zy=["POST","PUT","PATCH","DELETE"];new Set(Zy);var Lx=["GET",...Zy];new Set(Lx);var di=C.createContext(null);di.displayName="DataRouter";var br=C.createContext(null);br.displayName="DataRouterState";var Qy=C.createContext({isTransitioning:!1});Qy.displayName="ViewTransition";var Hx=C.createContext(new Map);Hx.displayName="Fetchers";var kx=C.createContext(null);kx.displayName="Await";var Je=C.createContext(null);Je.displayName="Navigation";var zl=C.createContext(null);zl.displayName="Location";var vn=C.createContext({outlet:null,matches:[],isDataRoute:!1});vn.displayName="Route";var Vc=C.createContext(null);Vc.displayName="RouteError";function qx(a,{relative:i}={}){jt(_l(),"useHref() may be used only in the context of a <Router> component.");let{basename:s,navigator:r}=C.useContext(Je),{hash:c,pathname:f,search:d}=Ul(a,{relative:i}),p=f;return s!=="/"&&(p=f==="/"?s:mn([s,f])),r.createHref({pathname:p,search:d,hash:c})}function _l(){return C.useContext(zl)!=null}function Qn(){return jt(_l(),"useLocation() may be used only in the context of a <Router> component."),C.useContext(zl).location}var Py="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function $y(a){C.useContext(Je).static||C.useLayoutEffect(a)}function Yx(){let{isDataRoute:a}=C.useContext(vn);return a?eb():Gx()}function Gx(){jt(_l(),"useNavigate() may be used only in the context of a <Router> component.");let a=C.useContext(di),{basename:i,navigator:s}=C.useContext(Je),{matches:r}=C.useContext(vn),{pathname:c}=Qn(),f=JSON.stringify(Xy(r)),d=C.useRef(!1);return $y(()=>{d.current=!0}),C.useCallback((m,h={})=>{if($e(d.current,Py),!d.current)return;if(typeof m=="number"){s.go(m);return}let g=Ky(m,JSON.parse(f),c,h.relative==="path");a==null&&i!=="/"&&(g.pathname=g.pathname==="/"?i:mn([i,g.pathname])),(h.replace?s.replace:s.push)(g,h.state,h)},[i,s,f,c,a])}C.createContext(null);function Ul(a,{relative:i}={}){let{matches:s}=C.useContext(vn),{pathname:r}=Qn(),c=JSON.stringify(Xy(s));return C.useMemo(()=>Ky(a,JSON.parse(c),r,i==="path"),[a,c,r,i])}function Xx(a,i){return Jy(a,i)}function Jy(a,i,s,r){var V;jt(_l(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:c}=C.useContext(Je),{matches:f}=C.useContext(vn),d=f[f.length-1],p=d?d.params:{},m=d?d.pathname:"/",h=d?d.pathnameBase:"/",g=d&&d.route;{let q=g&&g.path||"";Fy(m,!g||q.endsWith("*")||q.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${m}" (under <Route path="${q}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${q}"> to <Route path="${q==="/"?"*":`${q}/*`}">.`)}let x=Qn(),b;if(i){let q=typeof i=="string"?fi(i):i;jt(h==="/"||((V=q.pathname)==null?void 0:V.startsWith(h)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${h}" but pathname "${q.pathname}" was given in the \`location\` prop.`),b=q}else b=x;let M=b.pathname||"/",D=M;if(h!=="/"){let q=h.replace(/^\//,"").split("/");D="/"+M.replace(/^\//,"").split("/").slice(q.length).join("/")}let z=qy(a,{pathname:D});$e(g||z!=null,`No routes matched location "${b.pathname}${b.search}${b.hash}" `),$e(z==null||z[z.length-1].route.element!==void 0||z[z.length-1].route.Component!==void 0||z[z.length-1].route.lazy!==void 0,`Matched leaf route at location "${b.pathname}${b.search}${b.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let B=$x(z&&z.map(q=>Object.assign({},q,{params:Object.assign({},p,q.params),pathname:mn([h,c.encodeLocation?c.encodeLocation(q.pathname).pathname:q.pathname]),pathnameBase:q.pathnameBase==="/"?h:mn([h,c.encodeLocation?c.encodeLocation(q.pathnameBase).pathname:q.pathnameBase])})),f,s,r);return i&&B?C.createElement(zl.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...b},navigationType:"POP"}},B):B}function Kx(){let a=tb(),i=Bx(a)?`${a.status} ${a.statusText}`:a instanceof Error?a.message:JSON.stringify(a),s=a instanceof Error?a.stack:null,r="rgba(200,200,200, 0.5)",c={padding:"0.5rem",backgroundColor:r},f={padding:"2px 4px",backgroundColor:r},d=null;return console.error("Error handled by React Router default ErrorBoundary:",a),d=C.createElement(C.Fragment,null,C.createElement("p",null,"💿 Hey developer 👋"),C.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",C.createElement("code",{style:f},"ErrorBoundary")," or"," ",C.createElement("code",{style:f},"errorElement")," prop on your route.")),C.createElement(C.Fragment,null,C.createElement("h2",null,"Unexpected Application Error!"),C.createElement("h3",{style:{fontStyle:"italic"}},i),s?C.createElement("pre",{style:c},s):null,d)}var Zx=C.createElement(Kx,null),Qx=class extends C.Component{constructor(a){super(a),this.state={location:a.location,revalidation:a.revalidation,error:a.error}}static getDerivedStateFromError(a){return{error:a}}static getDerivedStateFromProps(a,i){return i.location!==a.location||i.revalidation!=="idle"&&a.revalidation==="idle"?{error:a.error,location:a.location,revalidation:a.revalidation}:{error:a.error!==void 0?a.error:i.error,location:i.location,revalidation:a.revalidation||i.revalidation}}componentDidCatch(a,i){console.error("React Router caught the following error during render",a,i)}render(){return this.state.error!==void 0?C.createElement(vn.Provider,{value:this.props.routeContext},C.createElement(Vc.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Px({routeContext:a,match:i,children:s}){let r=C.useContext(di);return r&&r.static&&r.staticContext&&(i.route.errorElement||i.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=i.route.id),C.createElement(vn.Provider,{value:a},s)}function $x(a,i=[],s=null,r=null){if(a==null){if(!s)return null;if(s.errors)a=s.matches;else if(i.length===0&&!s.initialized&&s.matches.length>0)a=s.matches;else return null}let c=a,f=s==null?void 0:s.errors;if(f!=null){let m=c.findIndex(h=>h.route.id&&(f==null?void 0:f[h.route.id])!==void 0);jt(m>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),c=c.slice(0,Math.min(c.length,m+1))}let d=!1,p=-1;if(s)for(let m=0;m<c.length;m++){let h=c[m];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(p=m),h.route.id){let{loaderData:g,errors:x}=s,b=h.route.loader&&!g.hasOwnProperty(h.route.id)&&(!x||x[h.route.id]===void 0);if(h.route.lazy||b){d=!0,p>=0?c=c.slice(0,p+1):c=[c[0]];break}}}return c.reduceRight((m,h,g)=>{let x,b=!1,M=null,D=null;s&&(x=f&&h.route.id?f[h.route.id]:void 0,M=h.route.errorElement||Zx,d&&(p<0&&g===0?(Fy("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),b=!0,D=null):p===g&&(b=!0,D=h.route.hydrateFallbackElement||null)));let z=i.concat(c.slice(0,g+1)),B=()=>{let V;return x?V=M:b?V=D:h.route.Component?V=C.createElement(h.route.Component,null):h.route.element?V=h.route.element:V=m,C.createElement(Px,{match:h,routeContext:{outlet:m,matches:z,isDataRoute:s!=null},children:V})};return s&&(h.route.ErrorBoundary||h.route.errorElement||g===0)?C.createElement(Qx,{location:s.location,revalidation:s.revalidation,component:M,error:x,children:B(),routeContext:{outlet:null,matches:z,isDataRoute:!0}}):B()},null)}function zc(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Jx(a){let i=C.useContext(di);return jt(i,zc(a)),i}function Fx(a){let i=C.useContext(br);return jt(i,zc(a)),i}function Wx(a){let i=C.useContext(vn);return jt(i,zc(a)),i}function _c(a){let i=Wx(a),s=i.matches[i.matches.length-1];return jt(s.route.id,`${a} can only be used on routes that contain a unique "id"`),s.route.id}function Ix(){return _c("useRouteId")}function tb(){var r;let a=C.useContext(Vc),i=Fx("useRouteError"),s=_c("useRouteError");return a!==void 0?a:(r=i.errors)==null?void 0:r[s]}function eb(){let{router:a}=Jx("useNavigate"),i=_c("useNavigate"),s=C.useRef(!1);return $y(()=>{s.current=!0}),C.useCallback(async(c,f={})=>{$e(s.current,Py),s.current&&(typeof c=="number"?a.navigate(c):await a.navigate(c,{fromRouteId:i,...f}))},[a,i])}var Ep={};function Fy(a,i,s){!i&&!Ep[a]&&(Ep[a]=!0,$e(!1,s))}C.memo(nb);function nb({routes:a,future:i,state:s}){return Jy(a,void 0,s,i)}function vl(a){jt(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function ab({basename:a="/",children:i=null,location:s,navigationType:r="POP",navigator:c,static:f=!1}){jt(!_l(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let d=a.replace(/^\/*/,"/"),p=C.useMemo(()=>({basename:d,navigator:c,static:f,future:{}}),[d,c,f]);typeof s=="string"&&(s=fi(s));let{pathname:m="/",search:h="",hash:g="",state:x=null,key:b="default"}=s,M=C.useMemo(()=>{let D=pn(m,d);return D==null?null:{location:{pathname:D,search:h,hash:g,state:x,key:b},navigationType:r}},[d,m,h,g,x,b,r]);return $e(M!=null,`<Router basename="${d}"> is not able to match the URL "${m}${h}${g}" because it does not start with the basename, so the <Router> won't render anything.`),M==null?null:C.createElement(Je.Provider,{value:p},C.createElement(zl.Provider,{children:i,value:M}))}function ib({children:a,location:i}){return Xx(uc(a),i)}function uc(a,i=[]){let s=[];return C.Children.forEach(a,(r,c)=>{if(!C.isValidElement(r))return;let f=[...i,c];if(r.type===C.Fragment){s.push.apply(s,uc(r.props.children,f));return}jt(r.type===vl,`[${typeof r.type=="string"?r.type:r.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),jt(!r.props.index||!r.props.children,"An index route cannot have child routes.");let d={id:r.props.id||f.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,hydrateFallbackElement:r.props.hydrateFallbackElement,HydrateFallback:r.props.HydrateFallback,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.hasErrorBoundary===!0||r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(d.children=uc(r.props.children,f)),s.push(d)}),s}var sr="get",rr="application/x-www-form-urlencoded";function Sr(a){return a!=null&&typeof a.tagName=="string"}function lb(a){return Sr(a)&&a.tagName.toLowerCase()==="button"}function sb(a){return Sr(a)&&a.tagName.toLowerCase()==="form"}function rb(a){return Sr(a)&&a.tagName.toLowerCase()==="input"}function ob(a){return!!(a.metaKey||a.altKey||a.ctrlKey||a.shiftKey)}function ub(a,i){return a.button===0&&(!i||i==="_self")&&!ob(a)}var nr=null;function cb(){if(nr===null)try{new FormData(document.createElement("form"),0),nr=!1}catch{nr=!0}return nr}var fb=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Pu(a){return a!=null&&!fb.has(a)?($e(!1,`"${a}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${rr}"`),null):a}function db(a,i){let s,r,c,f,d;if(sb(a)){let p=a.getAttribute("action");r=p?pn(p,i):null,s=a.getAttribute("method")||sr,c=Pu(a.getAttribute("enctype"))||rr,f=new FormData(a)}else if(lb(a)||rb(a)&&(a.type==="submit"||a.type==="image")){let p=a.form;if(p==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let m=a.getAttribute("formaction")||p.getAttribute("action");if(r=m?pn(m,i):null,s=a.getAttribute("formmethod")||p.getAttribute("method")||sr,c=Pu(a.getAttribute("formenctype"))||Pu(p.getAttribute("enctype"))||rr,f=new FormData(p,a),!cb()){let{name:h,type:g,value:x}=a;if(g==="image"){let b=h?`${h}.`:"";f.append(`${b}x`,"0"),f.append(`${b}y`,"0")}else h&&f.append(h,x)}}else{if(Sr(a))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');s=sr,r=null,c=rr,d=a}return f&&c==="text/plain"&&(d=f,f=void 0),{action:r,method:s.toLowerCase(),encType:c,formData:f,body:d}}function Uc(a,i){if(a===!1||a===null||typeof a>"u")throw new Error(i)}async function hb(a,i){if(a.id in i)return i[a.id];try{let s=await import(a.module);return i[a.id]=s,s}catch(s){return console.error(`Error loading route module \`${a.module}\`, reloading page...`),console.error(s),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function mb(a){return a==null?!1:a.href==null?a.rel==="preload"&&typeof a.imageSrcSet=="string"&&typeof a.imageSizes=="string":typeof a.rel=="string"&&typeof a.href=="string"}async function pb(a,i,s){let r=await Promise.all(a.map(async c=>{let f=i.routes[c.route.id];if(f){let d=await hb(f,s);return d.links?d.links():[]}return[]}));return xb(r.flat(1).filter(mb).filter(c=>c.rel==="stylesheet"||c.rel==="preload").map(c=>c.rel==="stylesheet"?{...c,rel:"prefetch",as:"style"}:{...c,rel:"prefetch"}))}function Mp(a,i,s,r,c,f){let d=(m,h)=>s[h]?m.route.id!==s[h].route.id:!0,p=(m,h)=>{var g;return s[h].pathname!==m.pathname||((g=s[h].route.path)==null?void 0:g.endsWith("*"))&&s[h].params["*"]!==m.params["*"]};return f==="assets"?i.filter((m,h)=>d(m,h)||p(m,h)):f==="data"?i.filter((m,h)=>{var x;let g=r.routes[m.route.id];if(!g||!g.hasLoader)return!1;if(d(m,h)||p(m,h))return!0;if(m.route.shouldRevalidate){let b=m.route.shouldRevalidate({currentUrl:new URL(c.pathname+c.search+c.hash,window.origin),currentParams:((x=s[0])==null?void 0:x.params)||{},nextUrl:new URL(a,window.origin),nextParams:m.params,defaultShouldRevalidate:!0});if(typeof b=="boolean")return b}return!0}):[]}function yb(a,i,{includeHydrateFallback:s}={}){return gb(a.map(r=>{let c=i.routes[r.route.id];if(!c)return[];let f=[c.module];return c.clientActionModule&&(f=f.concat(c.clientActionModule)),c.clientLoaderModule&&(f=f.concat(c.clientLoaderModule)),s&&c.hydrateFallbackModule&&(f=f.concat(c.hydrateFallbackModule)),c.imports&&(f=f.concat(c.imports)),f}).flat(1))}function gb(a){return[...new Set(a)]}function vb(a){let i={},s=Object.keys(a).sort();for(let r of s)i[r]=a[r];return i}function xb(a,i){let s=new Set;return new Set(i),a.reduce((r,c)=>{let f=JSON.stringify(vb(c));return s.has(f)||(s.add(f),r.push({key:f,link:c})),r},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var bb=new Set([100,101,204,205]);function Sb(a,i){let s=typeof a=="string"?new URL(a,typeof window>"u"?"server://singlefetch/":window.location.origin):a;return s.pathname==="/"?s.pathname="_root.data":i&&pn(s.pathname,i)==="/"?s.pathname=`${i.replace(/\/$/,"")}/_root.data`:s.pathname=`${s.pathname.replace(/\/$/,"")}.data`,s}function Wy(){let a=C.useContext(di);return Uc(a,"You must render this element inside a <DataRouterContext.Provider> element"),a}function Tb(){let a=C.useContext(br);return Uc(a,"You must render this element inside a <DataRouterStateContext.Provider> element"),a}var Bc=C.createContext(void 0);Bc.displayName="FrameworkContext";function Iy(){let a=C.useContext(Bc);return Uc(a,"You must render this element inside a <HydratedRouter> element"),a}function Ab(a,i){let s=C.useContext(Bc),[r,c]=C.useState(!1),[f,d]=C.useState(!1),{onFocus:p,onBlur:m,onMouseEnter:h,onMouseLeave:g,onTouchStart:x}=i,b=C.useRef(null);C.useEffect(()=>{if(a==="render"&&d(!0),a==="viewport"){let z=V=>{V.forEach(q=>{d(q.isIntersecting)})},B=new IntersectionObserver(z,{threshold:.5});return b.current&&B.observe(b.current),()=>{B.disconnect()}}},[a]),C.useEffect(()=>{if(r){let z=setTimeout(()=>{d(!0)},100);return()=>{clearTimeout(z)}}},[r]);let M=()=>{c(!0)},D=()=>{c(!1),d(!1)};return s?a!=="intent"?[f,b,{}]:[f,b,{onFocus:yl(p,M),onBlur:yl(m,D),onMouseEnter:yl(h,M),onMouseLeave:yl(g,D),onTouchStart:yl(x,M)}]:[!1,b,{}]}function yl(a,i){return s=>{a&&a(s),s.defaultPrevented||i(s)}}function Eb({page:a,...i}){let{router:s}=Wy(),r=C.useMemo(()=>qy(s.routes,a,s.basename),[s.routes,a,s.basename]);return r?C.createElement(wb,{page:a,matches:r,...i}):null}function Mb(a){let{manifest:i,routeModules:s}=Iy(),[r,c]=C.useState([]);return C.useEffect(()=>{let f=!1;return pb(a,i,s).then(d=>{f||c(d)}),()=>{f=!0}},[a,i,s]),r}function wb({page:a,matches:i,...s}){let r=Qn(),{manifest:c,routeModules:f}=Iy(),{basename:d}=Wy(),{loaderData:p,matches:m}=Tb(),h=C.useMemo(()=>Mp(a,i,m,c,r,"data"),[a,i,m,c,r]),g=C.useMemo(()=>Mp(a,i,m,c,r,"assets"),[a,i,m,c,r]),x=C.useMemo(()=>{if(a===r.pathname+r.search+r.hash)return[];let D=new Set,z=!1;if(i.forEach(V=>{var k;let q=c.routes[V.route.id];!q||!q.hasLoader||(!h.some(P=>P.route.id===V.route.id)&&V.route.id in p&&((k=f[V.route.id])!=null&&k.shouldRevalidate)||q.hasClientLoader?z=!0:D.add(V.route.id))}),D.size===0)return[];let B=Sb(a,d);return z&&D.size>0&&B.searchParams.set("_routes",i.filter(V=>D.has(V.route.id)).map(V=>V.route.id).join(",")),[B.pathname+B.search]},[d,p,r,c,h,i,a,f]),b=C.useMemo(()=>yb(g,c),[g,c]),M=Mb(g);return C.createElement(C.Fragment,null,x.map(D=>C.createElement("link",{key:D,rel:"prefetch",as:"fetch",href:D,...s})),b.map(D=>C.createElement("link",{key:D,rel:"modulepreload",href:D,...s})),M.map(({key:D,link:z})=>C.createElement("link",{key:D,...z})))}function Db(...a){return i=>{a.forEach(s=>{typeof s=="function"?s(i):s!=null&&(s.current=i)})}}var t0=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{t0&&(window.__reactRouterVersion="7.6.2")}catch{}function Rb({basename:a,children:i,window:s}){let r=C.useRef();r.current==null&&(r.current=mx({window:s,v5Compat:!0}));let c=r.current,[f,d]=C.useState({action:c.action,location:c.location}),p=C.useCallback(m=>{C.startTransition(()=>d(m))},[d]);return C.useLayoutEffect(()=>c.listen(p),[c,p]),C.createElement(ab,{basename:a,children:i,location:f.location,navigationType:f.action,navigator:c})}var e0=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,hr=C.forwardRef(function({onClick:i,discover:s="render",prefetch:r="none",relative:c,reloadDocument:f,replace:d,state:p,target:m,to:h,preventScrollReset:g,viewTransition:x,...b},M){let{basename:D}=C.useContext(Je),z=typeof h=="string"&&e0.test(h),B,V=!1;if(typeof h=="string"&&z&&(B=h,t0))try{let rt=new URL(window.location.href),bt=h.startsWith("//")?new URL(rt.protocol+h):new URL(h),Yt=pn(bt.pathname,D);bt.origin===rt.origin&&Yt!=null?h=Yt+bt.search+bt.hash:V=!0}catch{$e(!1,`<Link to="${h}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let q=qx(h,{relative:c}),[k,P,G]=Ab(r,b),at=Ob(h,{replace:d,state:p,target:m,preventScrollReset:g,relative:c,viewTransition:x});function lt(rt){i&&i(rt),rt.defaultPrevented||at(rt)}let J=C.createElement("a",{...b,...G,href:B||q,onClick:V||f?i:lt,ref:Db(M,P),target:m,"data-discover":!z&&s==="render"?"true":void 0});return k&&!z?C.createElement(C.Fragment,null,J,C.createElement(Eb,{page:q})):J});hr.displayName="Link";var Cb=C.forwardRef(function({"aria-current":i="page",caseSensitive:s=!1,className:r="",end:c=!1,style:f,to:d,viewTransition:p,children:m,...h},g){let x=Ul(d,{relative:h.relative}),b=Qn(),M=C.useContext(br),{navigator:D,basename:z}=C.useContext(Je),B=M!=null&&Bb(x)&&p===!0,V=D.encodeLocation?D.encodeLocation(x).pathname:x.pathname,q=b.pathname,k=M&&M.navigation&&M.navigation.location?M.navigation.location.pathname:null;s||(q=q.toLowerCase(),k=k?k.toLowerCase():null,V=V.toLowerCase()),k&&z&&(k=pn(k,z)||k);const P=V!=="/"&&V.endsWith("/")?V.length-1:V.length;let G=q===V||!c&&q.startsWith(V)&&q.charAt(P)==="/",at=k!=null&&(k===V||!c&&k.startsWith(V)&&k.charAt(V.length)==="/"),lt={isActive:G,isPending:at,isTransitioning:B},J=G?i:void 0,rt;typeof r=="function"?rt=r(lt):rt=[r,G?"active":null,at?"pending":null,B?"transitioning":null].filter(Boolean).join(" ");let bt=typeof f=="function"?f(lt):f;return C.createElement(hr,{...h,"aria-current":J,className:rt,ref:g,style:bt,to:d,viewTransition:p},typeof m=="function"?m(lt):m)});Cb.displayName="NavLink";var Nb=C.forwardRef(({discover:a="render",fetcherKey:i,navigate:s,reloadDocument:r,replace:c,state:f,method:d=sr,action:p,onSubmit:m,relative:h,preventScrollReset:g,viewTransition:x,...b},M)=>{let D=_b(),z=Ub(p,{relative:h}),B=d.toLowerCase()==="get"?"get":"post",V=typeof p=="string"&&e0.test(p),q=k=>{if(m&&m(k),k.defaultPrevented)return;k.preventDefault();let P=k.nativeEvent.submitter,G=(P==null?void 0:P.getAttribute("formmethod"))||d;D(P||k.currentTarget,{fetcherKey:i,method:G,navigate:s,replace:c,state:f,relative:h,preventScrollReset:g,viewTransition:x})};return C.createElement("form",{ref:M,method:B,action:z,onSubmit:r?m:q,...b,"data-discover":!V&&a==="render"?"true":void 0})});Nb.displayName="Form";function jb(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function n0(a){let i=C.useContext(di);return jt(i,jb(a)),i}function Ob(a,{target:i,replace:s,state:r,preventScrollReset:c,relative:f,viewTransition:d}={}){let p=Yx(),m=Qn(),h=Ul(a,{relative:f});return C.useCallback(g=>{if(ub(g,i)){g.preventDefault();let x=s!==void 0?s:wl(m)===wl(h);p(a,{replace:x,state:r,preventScrollReset:c,relative:f,viewTransition:d})}},[m,p,h,s,r,i,a,c,f,d])}var Vb=0,zb=()=>`__${String(++Vb)}__`;function _b(){let{router:a}=n0("useSubmit"),{basename:i}=C.useContext(Je),s=Ix();return C.useCallback(async(r,c={})=>{let{action:f,method:d,encType:p,formData:m,body:h}=db(r,i);if(c.navigate===!1){let g=c.fetcherKey||zb();await a.fetch(g,s,c.action||f,{preventScrollReset:c.preventScrollReset,formData:m,body:h,formMethod:c.method||d,formEncType:c.encType||p,flushSync:c.flushSync})}else await a.navigate(c.action||f,{preventScrollReset:c.preventScrollReset,formData:m,body:h,formMethod:c.method||d,formEncType:c.encType||p,replace:c.replace,state:c.state,fromRouteId:s,flushSync:c.flushSync,viewTransition:c.viewTransition})},[a,i,s])}function Ub(a,{relative:i}={}){let{basename:s}=C.useContext(Je),r=C.useContext(vn);jt(r,"useFormAction must be used inside a RouteContext");let[c]=r.matches.slice(-1),f={...Ul(a||".",{relative:i})},d=Qn();if(a==null){f.search=d.search;let p=new URLSearchParams(f.search),m=p.getAll("index");if(m.some(g=>g==="")){p.delete("index"),m.filter(x=>x).forEach(x=>p.append("index",x));let g=p.toString();f.search=g?`?${g}`:""}}return(!a||a===".")&&c.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),s!=="/"&&(f.pathname=f.pathname==="/"?s:mn([s,f.pathname])),wl(f)}function Bb(a,i={}){let s=C.useContext(Qy);jt(s!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=n0("useViewTransitionState"),c=Ul(a,{relative:i.relative});if(!s.isTransitioning)return!1;let f=pn(s.currentLocation.pathname,r)||s.currentLocation.pathname,d=pn(s.nextLocation.pathname,r)||s.nextLocation.pathname;return dr(c.pathname,d)!=null||dr(c.pathname,f)!=null}[...bb];const a0=C.createContext({});function Lb(a){const i=C.useRef(null);return i.current===null&&(i.current=a()),i.current}const Lc=typeof window<"u",Hb=Lc?C.useLayoutEffect:C.useEffect,Hc=C.createContext(null);function kc(a,i){a.indexOf(i)===-1&&a.push(i)}function qc(a,i){const s=a.indexOf(i);s>-1&&a.splice(s,1)}const yn=(a,i,s)=>s>i?i:s<a?a:s;let Yc=()=>{};const gn={},i0=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a);function l0(a){return typeof a=="object"&&a!==null}const s0=a=>/^0[^.\s]+$/u.test(a);function Gc(a){let i;return()=>(i===void 0&&(i=a()),i)}const ze=a=>a,kb=(a,i)=>s=>i(a(s)),Bl=(...a)=>a.reduce(kb),Dl=(a,i,s)=>{const r=i-a;return r===0?1:(s-a)/r};class Xc{constructor(){this.subscriptions=[]}add(i){return kc(this.subscriptions,i),()=>qc(this.subscriptions,i)}notify(i,s,r){const c=this.subscriptions.length;if(c)if(c===1)this.subscriptions[0](i,s,r);else for(let f=0;f<c;f++){const d=this.subscriptions[f];d&&d(i,s,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Ze=a=>a*1e3,Qe=a=>a/1e3;function r0(a,i){return i?a*(1e3/i):0}const o0=(a,i,s)=>(((1-3*s+3*i)*a+(3*s-6*i))*a+3*i)*a,qb=1e-7,Yb=12;function Gb(a,i,s,r,c){let f,d,p=0;do d=i+(s-i)/2,f=o0(d,r,c)-a,f>0?s=d:i=d;while(Math.abs(f)>qb&&++p<Yb);return d}function Ll(a,i,s,r){if(a===i&&s===r)return ze;const c=f=>Gb(f,0,1,a,s);return f=>f===0||f===1?f:o0(c(f),i,r)}const u0=a=>i=>i<=.5?a(2*i)/2:(2-a(2*(1-i)))/2,c0=a=>i=>1-a(1-i),f0=Ll(.33,1.53,.69,.99),Kc=c0(f0),d0=u0(Kc),h0=a=>(a*=2)<1?.5*Kc(a):.5*(2-Math.pow(2,-10*(a-1))),Zc=a=>1-Math.sin(Math.acos(a)),m0=c0(Zc),p0=u0(Zc),Xb=Ll(.42,0,1,1),Kb=Ll(0,0,.58,1),y0=Ll(.42,0,.58,1),Zb=a=>Array.isArray(a)&&typeof a[0]!="number",g0=a=>Array.isArray(a)&&typeof a[0]=="number",Qb={linear:ze,easeIn:Xb,easeInOut:y0,easeOut:Kb,circIn:Zc,circInOut:p0,circOut:m0,backIn:Kc,backInOut:d0,backOut:f0,anticipate:h0},Pb=a=>typeof a=="string",wp=a=>{if(g0(a)){Yc(a.length===4);const[i,s,r,c]=a;return Ll(i,s,r,c)}else if(Pb(a))return Qb[a];return a},ar=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],Dp={value:null};function $b(a,i){let s=new Set,r=new Set,c=!1,f=!1;const d=new WeakSet;let p={delta:0,timestamp:0,isProcessing:!1},m=0;function h(x){d.has(x)&&(g.schedule(x),a()),m++,x(p)}const g={schedule:(x,b=!1,M=!1)=>{const z=M&&c?s:r;return b&&d.add(x),z.has(x)||z.add(x),x},cancel:x=>{r.delete(x),d.delete(x)},process:x=>{if(p=x,c){f=!0;return}c=!0,[s,r]=[r,s],s.forEach(h),i&&Dp.value&&Dp.value.frameloop[i].push(m),m=0,s.clear(),c=!1,f&&(f=!1,g.process(x))}};return g}const Jb=40;function v0(a,i){let s=!1,r=!0;const c={delta:0,timestamp:0,isProcessing:!1},f=()=>s=!0,d=ar.reduce((k,P)=>(k[P]=$b(f,i?P:void 0),k),{}),{setup:p,read:m,resolveKeyframes:h,preUpdate:g,update:x,preRender:b,render:M,postRender:D}=d,z=()=>{const k=gn.useManualTiming?c.timestamp:performance.now();s=!1,gn.useManualTiming||(c.delta=r?1e3/60:Math.max(Math.min(k-c.timestamp,Jb),1)),c.timestamp=k,c.isProcessing=!0,p.process(c),m.process(c),h.process(c),g.process(c),x.process(c),b.process(c),M.process(c),D.process(c),c.isProcessing=!1,s&&i&&(r=!1,a(z))},B=()=>{s=!0,r=!0,c.isProcessing||a(z)};return{schedule:ar.reduce((k,P)=>{const G=d[P];return k[P]=(at,lt=!1,J=!1)=>(s||B(),G.schedule(at,lt,J)),k},{}),cancel:k=>{for(let P=0;P<ar.length;P++)d[ar[P]].cancel(k)},state:c,steps:d}}const{schedule:Ot,cancel:Kn,state:te,steps:$u}=v0(typeof requestAnimationFrame<"u"?requestAnimationFrame:ze,!0);let or;function Fb(){or=void 0}const de={now:()=>(or===void 0&&de.set(te.isProcessing||gn.useManualTiming?te.timestamp:performance.now()),or),set:a=>{or=a,queueMicrotask(Fb)}},x0=a=>i=>typeof i=="string"&&i.startsWith(a),Qc=x0("--"),Wb=x0("var(--"),Pc=a=>Wb(a)?Ib.test(a.split("/*")[0].trim()):!1,Ib=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,hi={test:a=>typeof a=="number",parse:parseFloat,transform:a=>a},Rl={...hi,transform:a=>yn(0,1,a)},ir={...hi,default:1},bl=a=>Math.round(a*1e5)/1e5,$c=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function tS(a){return a==null}const eS=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Jc=(a,i)=>s=>!!(typeof s=="string"&&eS.test(s)&&s.startsWith(a)||i&&!tS(s)&&Object.prototype.hasOwnProperty.call(s,i)),b0=(a,i,s)=>r=>{if(typeof r!="string")return r;const[c,f,d,p]=r.match($c);return{[a]:parseFloat(c),[i]:parseFloat(f),[s]:parseFloat(d),alpha:p!==void 0?parseFloat(p):1}},nS=a=>yn(0,255,a),Ju={...hi,transform:a=>Math.round(nS(a))},pa={test:Jc("rgb","red"),parse:b0("red","green","blue"),transform:({red:a,green:i,blue:s,alpha:r=1})=>"rgba("+Ju.transform(a)+", "+Ju.transform(i)+", "+Ju.transform(s)+", "+bl(Rl.transform(r))+")"};function aS(a){let i="",s="",r="",c="";return a.length>5?(i=a.substring(1,3),s=a.substring(3,5),r=a.substring(5,7),c=a.substring(7,9)):(i=a.substring(1,2),s=a.substring(2,3),r=a.substring(3,4),c=a.substring(4,5),i+=i,s+=s,r+=r,c+=c),{red:parseInt(i,16),green:parseInt(s,16),blue:parseInt(r,16),alpha:c?parseInt(c,16)/255:1}}const cc={test:Jc("#"),parse:aS,transform:pa.transform},Hl=a=>({test:i=>typeof i=="string"&&i.endsWith(a)&&i.split(" ").length===1,parse:parseFloat,transform:i=>`${i}${a}`}),Xn=Hl("deg"),Pe=Hl("%"),nt=Hl("px"),iS=Hl("vh"),lS=Hl("vw"),Rp={...Pe,parse:a=>Pe.parse(a)/100,transform:a=>Pe.transform(a*100)},ii={test:Jc("hsl","hue"),parse:b0("hue","saturation","lightness"),transform:({hue:a,saturation:i,lightness:s,alpha:r=1})=>"hsla("+Math.round(a)+", "+Pe.transform(bl(i))+", "+Pe.transform(bl(s))+", "+bl(Rl.transform(r))+")"},qt={test:a=>pa.test(a)||cc.test(a)||ii.test(a),parse:a=>pa.test(a)?pa.parse(a):ii.test(a)?ii.parse(a):cc.parse(a),transform:a=>typeof a=="string"?a:a.hasOwnProperty("red")?pa.transform(a):ii.transform(a),getAnimatableNone:a=>{const i=qt.parse(a);return i.alpha=0,qt.transform(i)}},sS=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function rS(a){var i,s;return isNaN(a)&&typeof a=="string"&&(((i=a.match($c))==null?void 0:i.length)||0)+(((s=a.match(sS))==null?void 0:s.length)||0)>0}const S0="number",T0="color",oS="var",uS="var(",Cp="${}",cS=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Cl(a){const i=a.toString(),s=[],r={color:[],number:[],var:[]},c=[];let f=0;const p=i.replace(cS,m=>(qt.test(m)?(r.color.push(f),c.push(T0),s.push(qt.parse(m))):m.startsWith(uS)?(r.var.push(f),c.push(oS),s.push(m)):(r.number.push(f),c.push(S0),s.push(parseFloat(m))),++f,Cp)).split(Cp);return{values:s,split:p,indexes:r,types:c}}function A0(a){return Cl(a).values}function E0(a){const{split:i,types:s}=Cl(a),r=i.length;return c=>{let f="";for(let d=0;d<r;d++)if(f+=i[d],c[d]!==void 0){const p=s[d];p===S0?f+=bl(c[d]):p===T0?f+=qt.transform(c[d]):f+=c[d]}return f}}const fS=a=>typeof a=="number"?0:qt.test(a)?qt.getAnimatableNone(a):a;function dS(a){const i=A0(a);return E0(a)(i.map(fS))}const Zn={test:rS,parse:A0,createTransformer:E0,getAnimatableNone:dS};function Fu(a,i,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?a+(i-a)*6*s:s<1/2?i:s<2/3?a+(i-a)*(2/3-s)*6:a}function hS({hue:a,saturation:i,lightness:s,alpha:r}){a/=360,i/=100,s/=100;let c=0,f=0,d=0;if(!i)c=f=d=s;else{const p=s<.5?s*(1+i):s+i-s*i,m=2*s-p;c=Fu(m,p,a+1/3),f=Fu(m,p,a),d=Fu(m,p,a-1/3)}return{red:Math.round(c*255),green:Math.round(f*255),blue:Math.round(d*255),alpha:r}}function mr(a,i){return s=>s>0?i:a}const Nt=(a,i,s)=>a+(i-a)*s,Wu=(a,i,s)=>{const r=a*a,c=s*(i*i-r)+r;return c<0?0:Math.sqrt(c)},mS=[cc,pa,ii],pS=a=>mS.find(i=>i.test(a));function Np(a){const i=pS(a);if(!i)return!1;let s=i.parse(a);return i===ii&&(s=hS(s)),s}const jp=(a,i)=>{const s=Np(a),r=Np(i);if(!s||!r)return mr(a,i);const c={...s};return f=>(c.red=Wu(s.red,r.red,f),c.green=Wu(s.green,r.green,f),c.blue=Wu(s.blue,r.blue,f),c.alpha=Nt(s.alpha,r.alpha,f),pa.transform(c))},fc=new Set(["none","hidden"]);function yS(a,i){return fc.has(a)?s=>s<=0?a:i:s=>s>=1?i:a}function gS(a,i){return s=>Nt(a,i,s)}function Fc(a){return typeof a=="number"?gS:typeof a=="string"?Pc(a)?mr:qt.test(a)?jp:bS:Array.isArray(a)?M0:typeof a=="object"?qt.test(a)?jp:vS:mr}function M0(a,i){const s=[...a],r=s.length,c=a.map((f,d)=>Fc(f)(f,i[d]));return f=>{for(let d=0;d<r;d++)s[d]=c[d](f);return s}}function vS(a,i){const s={...a,...i},r={};for(const c in s)a[c]!==void 0&&i[c]!==void 0&&(r[c]=Fc(a[c])(a[c],i[c]));return c=>{for(const f in r)s[f]=r[f](c);return s}}function xS(a,i){const s=[],r={color:0,var:0,number:0};for(let c=0;c<i.values.length;c++){const f=i.types[c],d=a.indexes[f][r[f]],p=a.values[d]??0;s[c]=p,r[f]++}return s}const bS=(a,i)=>{const s=Zn.createTransformer(i),r=Cl(a),c=Cl(i);return r.indexes.var.length===c.indexes.var.length&&r.indexes.color.length===c.indexes.color.length&&r.indexes.number.length>=c.indexes.number.length?fc.has(a)&&!c.values.length||fc.has(i)&&!r.values.length?yS(a,i):Bl(M0(xS(r,c),c.values),s):mr(a,i)};function w0(a,i,s){return typeof a=="number"&&typeof i=="number"&&typeof s=="number"?Nt(a,i,s):Fc(a)(a,i)}const SS=a=>{const i=({timestamp:s})=>a(s);return{start:(s=!0)=>Ot.update(i,s),stop:()=>Kn(i),now:()=>te.isProcessing?te.timestamp:de.now()}},D0=(a,i,s=10)=>{let r="";const c=Math.max(Math.round(i/s),2);for(let f=0;f<c;f++)r+=Math.round(a(f/(c-1))*1e4)/1e4+", ";return`linear(${r.substring(0,r.length-2)})`},pr=2e4;function Wc(a){let i=0;const s=50;let r=a.next(i);for(;!r.done&&i<pr;)i+=s,r=a.next(i);return i>=pr?1/0:i}function TS(a,i=100,s){const r=s({...a,keyframes:[0,i]}),c=Math.min(Wc(r),pr);return{type:"keyframes",ease:f=>r.next(c*f).value/i,duration:Qe(c)}}const AS=5;function R0(a,i,s){const r=Math.max(i-AS,0);return r0(s-a(r),i-r)}const _t={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Iu=.001;function ES({duration:a=_t.duration,bounce:i=_t.bounce,velocity:s=_t.velocity,mass:r=_t.mass}){let c,f,d=1-i;d=yn(_t.minDamping,_t.maxDamping,d),a=yn(_t.minDuration,_t.maxDuration,Qe(a)),d<1?(c=h=>{const g=h*d,x=g*a,b=g-s,M=dc(h,d),D=Math.exp(-x);return Iu-b/M*D},f=h=>{const x=h*d*a,b=x*s+s,M=Math.pow(d,2)*Math.pow(h,2)*a,D=Math.exp(-x),z=dc(Math.pow(h,2),d);return(-c(h)+Iu>0?-1:1)*((b-M)*D)/z}):(c=h=>{const g=Math.exp(-h*a),x=(h-s)*a+1;return-Iu+g*x},f=h=>{const g=Math.exp(-h*a),x=(s-h)*(a*a);return g*x});const p=5/a,m=wS(c,f,p);if(a=Ze(a),isNaN(m))return{stiffness:_t.stiffness,damping:_t.damping,duration:a};{const h=Math.pow(m,2)*r;return{stiffness:h,damping:d*2*Math.sqrt(r*h),duration:a}}}const MS=12;function wS(a,i,s){let r=s;for(let c=1;c<MS;c++)r=r-a(r)/i(r);return r}function dc(a,i){return a*Math.sqrt(1-i*i)}const DS=["duration","bounce"],RS=["stiffness","damping","mass"];function Op(a,i){return i.some(s=>a[s]!==void 0)}function CS(a){let i={velocity:_t.velocity,stiffness:_t.stiffness,damping:_t.damping,mass:_t.mass,isResolvedFromDuration:!1,...a};if(!Op(a,RS)&&Op(a,DS))if(a.visualDuration){const s=a.visualDuration,r=2*Math.PI/(s*1.2),c=r*r,f=2*yn(.05,1,1-(a.bounce||0))*Math.sqrt(c);i={...i,mass:_t.mass,stiffness:c,damping:f}}else{const s=ES(a);i={...i,...s,mass:_t.mass},i.isResolvedFromDuration=!0}return i}function yr(a=_t.visualDuration,i=_t.bounce){const s=typeof a!="object"?{visualDuration:a,keyframes:[0,1],bounce:i}:a;let{restSpeed:r,restDelta:c}=s;const f=s.keyframes[0],d=s.keyframes[s.keyframes.length-1],p={done:!1,value:f},{stiffness:m,damping:h,mass:g,duration:x,velocity:b,isResolvedFromDuration:M}=CS({...s,velocity:-Qe(s.velocity||0)}),D=b||0,z=h/(2*Math.sqrt(m*g)),B=d-f,V=Qe(Math.sqrt(m/g)),q=Math.abs(B)<5;r||(r=q?_t.restSpeed.granular:_t.restSpeed.default),c||(c=q?_t.restDelta.granular:_t.restDelta.default);let k;if(z<1){const G=dc(V,z);k=at=>{const lt=Math.exp(-z*V*at);return d-lt*((D+z*V*B)/G*Math.sin(G*at)+B*Math.cos(G*at))}}else if(z===1)k=G=>d-Math.exp(-V*G)*(B+(D+V*B)*G);else{const G=V*Math.sqrt(z*z-1);k=at=>{const lt=Math.exp(-z*V*at),J=Math.min(G*at,300);return d-lt*((D+z*V*B)*Math.sinh(J)+G*B*Math.cosh(J))/G}}const P={calculatedDuration:M&&x||null,next:G=>{const at=k(G);if(M)p.done=G>=x;else{let lt=G===0?D:0;z<1&&(lt=G===0?Ze(D):R0(k,G,at));const J=Math.abs(lt)<=r,rt=Math.abs(d-at)<=c;p.done=J&&rt}return p.value=p.done?d:at,p},toString:()=>{const G=Math.min(Wc(P),pr),at=D0(lt=>P.next(G*lt).value,G,30);return G+"ms "+at},toTransition:()=>{}};return P}yr.applyToOptions=a=>{const i=TS(a,100,yr);return a.ease=i.ease,a.duration=Ze(i.duration),a.type="keyframes",a};function hc({keyframes:a,velocity:i=0,power:s=.8,timeConstant:r=325,bounceDamping:c=10,bounceStiffness:f=500,modifyTarget:d,min:p,max:m,restDelta:h=.5,restSpeed:g}){const x=a[0],b={done:!1,value:x},M=J=>p!==void 0&&J<p||m!==void 0&&J>m,D=J=>p===void 0?m:m===void 0||Math.abs(p-J)<Math.abs(m-J)?p:m;let z=s*i;const B=x+z,V=d===void 0?B:d(B);V!==B&&(z=V-x);const q=J=>-z*Math.exp(-J/r),k=J=>V+q(J),P=J=>{const rt=q(J),bt=k(J);b.done=Math.abs(rt)<=h,b.value=b.done?V:bt};let G,at;const lt=J=>{M(b.value)&&(G=J,at=yr({keyframes:[b.value,D(b.value)],velocity:R0(k,J,b.value),damping:c,stiffness:f,restDelta:h,restSpeed:g}))};return lt(0),{calculatedDuration:null,next:J=>{let rt=!1;return!at&&G===void 0&&(rt=!0,P(J),lt(J)),G!==void 0&&J>=G?at.next(J-G):(!rt&&P(J),b)}}}function NS(a,i,s){const r=[],c=s||gn.mix||w0,f=a.length-1;for(let d=0;d<f;d++){let p=c(a[d],a[d+1]);if(i){const m=Array.isArray(i)?i[d]||ze:i;p=Bl(m,p)}r.push(p)}return r}function jS(a,i,{clamp:s=!0,ease:r,mixer:c}={}){const f=a.length;if(Yc(f===i.length),f===1)return()=>i[0];if(f===2&&i[0]===i[1])return()=>i[1];const d=a[0]===a[1];a[0]>a[f-1]&&(a=[...a].reverse(),i=[...i].reverse());const p=NS(i,r,c),m=p.length,h=g=>{if(d&&g<a[0])return i[0];let x=0;if(m>1)for(;x<a.length-2&&!(g<a[x+1]);x++);const b=Dl(a[x],a[x+1],g);return p[x](b)};return s?g=>h(yn(a[0],a[f-1],g)):h}function OS(a,i){const s=a[a.length-1];for(let r=1;r<=i;r++){const c=Dl(0,i,r);a.push(Nt(s,1,c))}}function VS(a){const i=[0];return OS(i,a.length-1),i}function zS(a,i){return a.map(s=>s*i)}function _S(a,i){return a.map(()=>i||y0).splice(0,a.length-1)}function Sl({duration:a=300,keyframes:i,times:s,ease:r="easeInOut"}){const c=Zb(r)?r.map(wp):wp(r),f={done:!1,value:i[0]},d=zS(s&&s.length===i.length?s:VS(i),a),p=jS(d,i,{ease:Array.isArray(c)?c:_S(i,c)});return{calculatedDuration:a,next:m=>(f.value=p(m),f.done=m>=a,f)}}const US=a=>a!==null;function Ic(a,{repeat:i,repeatType:s="loop"},r,c=1){const f=a.filter(US),p=c<0||i&&s!=="loop"&&i%2===1?0:f.length-1;return!p||r===void 0?f[p]:r}const BS={decay:hc,inertia:hc,tween:Sl,keyframes:Sl,spring:yr};function C0(a){typeof a.type=="string"&&(a.type=BS[a.type])}class tf{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(i=>{this.resolve=i})}notifyFinished(){this.resolve()}then(i,s){return this.finished.then(i,s)}}const LS=a=>a/100;class ef extends tf{constructor(i){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var r,c;const{motionValue:s}=this.options;s&&s.updatedAt!==de.now()&&this.tick(de.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(c=(r=this.options).onStop)==null||c.call(r))},this.options=i,this.initAnimation(),this.play(),i.autoplay===!1&&this.pause()}initAnimation(){const{options:i}=this;C0(i);const{type:s=Sl,repeat:r=0,repeatDelay:c=0,repeatType:f,velocity:d=0}=i;let{keyframes:p}=i;const m=s||Sl;m!==Sl&&typeof p[0]!="number"&&(this.mixKeyframes=Bl(LS,w0(p[0],p[1])),p=[0,100]);const h=m({...i,keyframes:p});f==="mirror"&&(this.mirroredGenerator=m({...i,keyframes:[...p].reverse(),velocity:-d})),h.calculatedDuration===null&&(h.calculatedDuration=Wc(h));const{calculatedDuration:g}=h;this.calculatedDuration=g,this.resolvedDuration=g+c,this.totalDuration=this.resolvedDuration*(r+1)-c,this.generator=h}updateTime(i){const s=Math.round(i-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=s}tick(i,s=!1){const{generator:r,totalDuration:c,mixKeyframes:f,mirroredGenerator:d,resolvedDuration:p,calculatedDuration:m}=this;if(this.startTime===null)return r.next(0);const{delay:h=0,keyframes:g,repeat:x,repeatType:b,repeatDelay:M,type:D,onUpdate:z,finalKeyframe:B}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,i):this.speed<0&&(this.startTime=Math.min(i-c/this.speed,this.startTime)),s?this.currentTime=i:this.updateTime(i);const V=this.currentTime-h*(this.playbackSpeed>=0?1:-1),q=this.playbackSpeed>=0?V<0:V>c;this.currentTime=Math.max(V,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let k=this.currentTime,P=r;if(x){const J=Math.min(this.currentTime,c)/p;let rt=Math.floor(J),bt=J%1;!bt&&J>=1&&(bt=1),bt===1&&rt--,rt=Math.min(rt,x+1),!!(rt%2)&&(b==="reverse"?(bt=1-bt,M&&(bt-=M/p)):b==="mirror"&&(P=d)),k=yn(0,1,bt)*p}const G=q?{done:!1,value:g[0]}:P.next(k);f&&(G.value=f(G.value));let{done:at}=G;!q&&m!==null&&(at=this.playbackSpeed>=0?this.currentTime>=c:this.currentTime<=0);const lt=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&at);return lt&&D!==hc&&(G.value=Ic(g,this.options,B,this.speed)),z&&z(G.value),lt&&this.finish(),G}then(i,s){return this.finished.then(i,s)}get duration(){return Qe(this.calculatedDuration)}get time(){return Qe(this.currentTime)}set time(i){var s;i=Ze(i),this.currentTime=i,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=i:this.driver&&(this.startTime=this.driver.now()-i/this.playbackSpeed),(s=this.driver)==null||s.start(!1)}get speed(){return this.playbackSpeed}set speed(i){this.updateTime(de.now());const s=this.playbackSpeed!==i;this.playbackSpeed=i,s&&(this.time=Qe(this.currentTime))}play(){var c,f;if(this.isStopped)return;const{driver:i=SS,startTime:s}=this.options;this.driver||(this.driver=i(d=>this.tick(d))),(f=(c=this.options).onPlay)==null||f.call(c);const r=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=r):this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime||(this.startTime=s??r),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(de.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var i,s;this.notifyFinished(),this.teardown(),this.state="finished",(s=(i=this.options).onComplete)==null||s.call(i)}cancel(){var i,s;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(s=(i=this.options).onCancel)==null||s.call(i)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(i){return this.startTime=0,this.tick(i,!0)}attachTimeline(i){var s;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(s=this.driver)==null||s.stop(),i.observe(this)}}function HS(a){for(let i=1;i<a.length;i++)a[i]??(a[i]=a[i-1])}const ya=a=>a*180/Math.PI,mc=a=>{const i=ya(Math.atan2(a[1],a[0]));return pc(i)},kS={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:a=>(Math.abs(a[0])+Math.abs(a[3]))/2,rotate:mc,rotateZ:mc,skewX:a=>ya(Math.atan(a[1])),skewY:a=>ya(Math.atan(a[2])),skew:a=>(Math.abs(a[1])+Math.abs(a[2]))/2},pc=a=>(a=a%360,a<0&&(a+=360),a),Vp=mc,zp=a=>Math.sqrt(a[0]*a[0]+a[1]*a[1]),_p=a=>Math.sqrt(a[4]*a[4]+a[5]*a[5]),qS={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:zp,scaleY:_p,scale:a=>(zp(a)+_p(a))/2,rotateX:a=>pc(ya(Math.atan2(a[6],a[5]))),rotateY:a=>pc(ya(Math.atan2(-a[2],a[0]))),rotateZ:Vp,rotate:Vp,skewX:a=>ya(Math.atan(a[4])),skewY:a=>ya(Math.atan(a[1])),skew:a=>(Math.abs(a[1])+Math.abs(a[4]))/2};function yc(a){return a.includes("scale")?1:0}function gc(a,i){if(!a||a==="none")return yc(i);const s=a.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let r,c;if(s)r=qS,c=s;else{const p=a.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=kS,c=p}if(!c)return yc(i);const f=r[i],d=c[1].split(",").map(GS);return typeof f=="function"?f(d):d[f]}const YS=(a,i)=>{const{transform:s="none"}=getComputedStyle(a);return gc(s,i)};function GS(a){return parseFloat(a.trim())}const mi=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],pi=new Set(mi),Up=a=>a===hi||a===nt,XS=new Set(["x","y","z"]),KS=mi.filter(a=>!XS.has(a));function ZS(a){const i=[];return KS.forEach(s=>{const r=a.getValue(s);r!==void 0&&(i.push([s,r.get()]),r.set(s.startsWith("scale")?1:0))}),i}const ga={width:({x:a},{paddingLeft:i="0",paddingRight:s="0"})=>a.max-a.min-parseFloat(i)-parseFloat(s),height:({y:a},{paddingTop:i="0",paddingBottom:s="0"})=>a.max-a.min-parseFloat(i)-parseFloat(s),top:(a,{top:i})=>parseFloat(i),left:(a,{left:i})=>parseFloat(i),bottom:({y:a},{top:i})=>parseFloat(i)+(a.max-a.min),right:({x:a},{left:i})=>parseFloat(i)+(a.max-a.min),x:(a,{transform:i})=>gc(i,"x"),y:(a,{transform:i})=>gc(i,"y")};ga.translateX=ga.x;ga.translateY=ga.y;const va=new Set;let vc=!1,xc=!1,bc=!1;function N0(){if(xc){const a=Array.from(va).filter(r=>r.needsMeasurement),i=new Set(a.map(r=>r.element)),s=new Map;i.forEach(r=>{const c=ZS(r);c.length&&(s.set(r,c),r.render())}),a.forEach(r=>r.measureInitialState()),i.forEach(r=>{r.render();const c=s.get(r);c&&c.forEach(([f,d])=>{var p;(p=r.getValue(f))==null||p.set(d)})}),a.forEach(r=>r.measureEndState()),a.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}xc=!1,vc=!1,va.forEach(a=>a.complete(bc)),va.clear()}function j0(){va.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(xc=!0)})}function QS(){bc=!0,j0(),N0(),bc=!1}class nf{constructor(i,s,r,c,f,d=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...i],this.onComplete=s,this.name=r,this.motionValue=c,this.element=f,this.isAsync=d}scheduleResolve(){this.state="scheduled",this.isAsync?(va.add(this),vc||(vc=!0,Ot.read(j0),Ot.resolveKeyframes(N0))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:i,name:s,element:r,motionValue:c}=this;if(i[0]===null){const f=c==null?void 0:c.get(),d=i[i.length-1];if(f!==void 0)i[0]=f;else if(r&&s){const p=r.readValue(s,d);p!=null&&(i[0]=p)}i[0]===void 0&&(i[0]=d),c&&f===void 0&&c.set(i[0])}HS(i)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(i=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,i),va.delete(this)}cancel(){this.state==="scheduled"&&(va.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const PS=a=>a.startsWith("--");function $S(a,i,s){PS(i)?a.style.setProperty(i,s):a.style[i]=s}const JS=Gc(()=>window.ScrollTimeline!==void 0),FS={};function WS(a,i){const s=Gc(a);return()=>FS[i]??s()}const O0=WS(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),xl=([a,i,s,r])=>`cubic-bezier(${a}, ${i}, ${s}, ${r})`,Bp={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:xl([0,.65,.55,1]),circOut:xl([.55,0,1,.45]),backIn:xl([.31,.01,.66,-.59]),backOut:xl([.33,1.53,.69,.99])};function V0(a,i){if(a)return typeof a=="function"?O0()?D0(a,i):"ease-out":g0(a)?xl(a):Array.isArray(a)?a.map(s=>V0(s,i)||Bp.easeOut):Bp[a]}function IS(a,i,s,{delay:r=0,duration:c=300,repeat:f=0,repeatType:d="loop",ease:p="easeOut",times:m}={},h=void 0){const g={[i]:s};m&&(g.offset=m);const x=V0(p,c);Array.isArray(x)&&(g.easing=x);const b={delay:r,duration:c,easing:Array.isArray(x)?"linear":x,fill:"both",iterations:f+1,direction:d==="reverse"?"alternate":"normal"};return h&&(b.pseudoElement=h),a.animate(g,b)}function z0(a){return typeof a=="function"&&"applyToOptions"in a}function t2({type:a,...i}){return z0(a)&&O0()?a.applyToOptions(i):(i.duration??(i.duration=300),i.ease??(i.ease="easeOut"),i)}class e2 extends tf{constructor(i){if(super(),this.finishedTime=null,this.isStopped=!1,!i)return;const{element:s,name:r,keyframes:c,pseudoElement:f,allowFlatten:d=!1,finalKeyframe:p,onComplete:m}=i;this.isPseudoElement=!!f,this.allowFlatten=d,this.options=i,Yc(typeof i.type!="string");const h=t2(i);this.animation=IS(s,r,c,h,f),h.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!f){const g=Ic(c,this.options,p,this.speed);this.updateMotionValue?this.updateMotionValue(g):$S(s,r,g),this.animation.cancel()}m==null||m(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var i,s;(s=(i=this.animation).finish)==null||s.call(i)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:i}=this;i==="idle"||i==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var i,s;this.isPseudoElement||(s=(i=this.animation).commitStyles)==null||s.call(i)}get duration(){var s,r;const i=((r=(s=this.animation.effect)==null?void 0:s.getComputedTiming)==null?void 0:r.call(s).duration)||0;return Qe(Number(i))}get time(){return Qe(Number(this.animation.currentTime)||0)}set time(i){this.finishedTime=null,this.animation.currentTime=Ze(i)}get speed(){return this.animation.playbackRate}set speed(i){i<0&&(this.finishedTime=null),this.animation.playbackRate=i}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(i){this.animation.startTime=i}attachTimeline({timeline:i,observe:s}){var r;return this.allowFlatten&&((r=this.animation.effect)==null||r.updateTiming({easing:"linear"})),this.animation.onfinish=null,i&&JS()?(this.animation.timeline=i,ze):s(this)}}const _0={anticipate:h0,backInOut:d0,circInOut:p0};function n2(a){return a in _0}function a2(a){typeof a.ease=="string"&&n2(a.ease)&&(a.ease=_0[a.ease])}const Lp=10;class i2 extends e2{constructor(i){a2(i),C0(i),super(i),i.startTime&&(this.startTime=i.startTime),this.options=i}updateMotionValue(i){const{motionValue:s,onUpdate:r,onComplete:c,element:f,...d}=this.options;if(!s)return;if(i!==void 0){s.set(i);return}const p=new ef({...d,autoplay:!1}),m=Ze(this.finishedTime??this.time);s.setWithVelocity(p.sample(m-Lp).value,p.sample(m).value,Lp),p.stop()}}const Hp=(a,i)=>i==="zIndex"?!1:!!(typeof a=="number"||Array.isArray(a)||typeof a=="string"&&(Zn.test(a)||a==="0")&&!a.startsWith("url("));function l2(a){const i=a[0];if(a.length===1)return!0;for(let s=0;s<a.length;s++)if(a[s]!==i)return!0}function s2(a,i,s,r){const c=a[0];if(c===null)return!1;if(i==="display"||i==="visibility")return!0;const f=a[a.length-1],d=Hp(c,i),p=Hp(f,i);return!d||!p?!1:l2(a)||(s==="spring"||z0(s))&&r}function U0(a){return l0(a)&&"offsetHeight"in a}const r2=new Set(["opacity","clipPath","filter","transform"]),o2=Gc(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function u2(a){var h;const{motionValue:i,name:s,repeatDelay:r,repeatType:c,damping:f,type:d}=a;if(!U0((h=i==null?void 0:i.owner)==null?void 0:h.current))return!1;const{onUpdate:p,transformTemplate:m}=i.owner.getProps();return o2()&&s&&r2.has(s)&&(s!=="transform"||!m)&&!p&&!r&&c!=="mirror"&&f!==0&&d!=="inertia"}const c2=40;class f2 extends tf{constructor({autoplay:i=!0,delay:s=0,type:r="keyframes",repeat:c=0,repeatDelay:f=0,repeatType:d="loop",keyframes:p,name:m,motionValue:h,element:g,...x}){var D;super(),this.stop=()=>{var z,B;this._animation&&(this._animation.stop(),(z=this.stopTimeline)==null||z.call(this)),(B=this.keyframeResolver)==null||B.cancel()},this.createdAt=de.now();const b={autoplay:i,delay:s,type:r,repeat:c,repeatDelay:f,repeatType:d,name:m,motionValue:h,element:g,...x},M=(g==null?void 0:g.KeyframeResolver)||nf;this.keyframeResolver=new M(p,(z,B,V)=>this.onKeyframesResolved(z,B,b,!V),m,h,g),(D=this.keyframeResolver)==null||D.scheduleResolve()}onKeyframesResolved(i,s,r,c){this.keyframeResolver=void 0;const{name:f,type:d,velocity:p,delay:m,isHandoff:h,onUpdate:g}=r;this.resolvedAt=de.now(),s2(i,f,d,p)||((gn.instantAnimations||!m)&&(g==null||g(Ic(i,r,s))),i[0]=i[i.length-1],r.duration=0,r.repeat=0);const b={startTime:c?this.resolvedAt?this.resolvedAt-this.createdAt>c2?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:s,...r,keyframes:i},M=!h&&u2(b)?new i2({...b,element:b.motionValue.owner.current}):new ef(b);M.finished.then(()=>this.notifyFinished()).catch(ze),this.pendingTimeline&&(this.stopTimeline=M.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=M}get finished(){return this._animation?this.animation.finished:this._finished}then(i,s){return this.finished.finally(i).then(()=>{})}get animation(){var i;return this._animation||((i=this.keyframeResolver)==null||i.resume(),QS()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(i){this.animation.time=i}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(i){this.animation.speed=i}get startTime(){return this.animation.startTime}attachTimeline(i){return this._animation?this.stopTimeline=this.animation.attachTimeline(i):this.pendingTimeline=i,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var i;this._animation&&this.animation.cancel(),(i=this.keyframeResolver)==null||i.cancel()}}const d2=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function h2(a){const i=d2.exec(a);if(!i)return[,];const[,s,r,c]=i;return[`--${s??r}`,c]}function B0(a,i,s=1){const[r,c]=h2(a);if(!r)return;const f=window.getComputedStyle(i).getPropertyValue(r);if(f){const d=f.trim();return i0(d)?parseFloat(d):d}return Pc(c)?B0(c,i,s+1):c}function af(a,i){return(a==null?void 0:a[i])??(a==null?void 0:a.default)??a}const L0=new Set(["width","height","top","left","right","bottom",...mi]),m2={test:a=>a==="auto",parse:a=>a},H0=a=>i=>i.test(a),k0=[hi,nt,Pe,Xn,lS,iS,m2],kp=a=>k0.find(H0(a));function p2(a){return typeof a=="number"?a===0:a!==null?a==="none"||a==="0"||s0(a):!0}const y2=new Set(["brightness","contrast","saturate","opacity"]);function g2(a){const[i,s]=a.slice(0,-1).split("(");if(i==="drop-shadow")return a;const[r]=s.match($c)||[];if(!r)return a;const c=s.replace(r,"");let f=y2.has(i)?1:0;return r!==s&&(f*=100),i+"("+f+c+")"}const v2=/\b([a-z-]*)\(.*?\)/gu,Sc={...Zn,getAnimatableNone:a=>{const i=a.match(v2);return i?i.map(g2).join(" "):a}},qp={...hi,transform:Math.round},x2={rotate:Xn,rotateX:Xn,rotateY:Xn,rotateZ:Xn,scale:ir,scaleX:ir,scaleY:ir,scaleZ:ir,skew:Xn,skewX:Xn,skewY:Xn,distance:nt,translateX:nt,translateY:nt,translateZ:nt,x:nt,y:nt,z:nt,perspective:nt,transformPerspective:nt,opacity:Rl,originX:Rp,originY:Rp,originZ:nt},lf={borderWidth:nt,borderTopWidth:nt,borderRightWidth:nt,borderBottomWidth:nt,borderLeftWidth:nt,borderRadius:nt,radius:nt,borderTopLeftRadius:nt,borderTopRightRadius:nt,borderBottomRightRadius:nt,borderBottomLeftRadius:nt,width:nt,maxWidth:nt,height:nt,maxHeight:nt,top:nt,right:nt,bottom:nt,left:nt,padding:nt,paddingTop:nt,paddingRight:nt,paddingBottom:nt,paddingLeft:nt,margin:nt,marginTop:nt,marginRight:nt,marginBottom:nt,marginLeft:nt,backgroundPositionX:nt,backgroundPositionY:nt,...x2,zIndex:qp,fillOpacity:Rl,strokeOpacity:Rl,numOctaves:qp},b2={...lf,color:qt,backgroundColor:qt,outlineColor:qt,fill:qt,stroke:qt,borderColor:qt,borderTopColor:qt,borderRightColor:qt,borderBottomColor:qt,borderLeftColor:qt,filter:Sc,WebkitFilter:Sc},q0=a=>b2[a];function Y0(a,i){let s=q0(a);return s!==Sc&&(s=Zn),s.getAnimatableNone?s.getAnimatableNone(i):void 0}const S2=new Set(["auto","none","0"]);function T2(a,i,s){let r=0,c;for(;r<a.length&&!c;){const f=a[r];typeof f=="string"&&!S2.has(f)&&Cl(f).values.length&&(c=a[r]),r++}if(c&&s)for(const f of i)a[f]=Y0(s,c)}class A2 extends nf{constructor(i,s,r,c,f){super(i,s,r,c,f,!0)}readKeyframes(){const{unresolvedKeyframes:i,element:s,name:r}=this;if(!s||!s.current)return;super.readKeyframes();for(let m=0;m<i.length;m++){let h=i[m];if(typeof h=="string"&&(h=h.trim(),Pc(h))){const g=B0(h,s.current);g!==void 0&&(i[m]=g),m===i.length-1&&(this.finalKeyframe=h)}}if(this.resolveNoneKeyframes(),!L0.has(r)||i.length!==2)return;const[c,f]=i,d=kp(c),p=kp(f);if(d!==p)if(Up(d)&&Up(p))for(let m=0;m<i.length;m++){const h=i[m];typeof h=="string"&&(i[m]=parseFloat(h))}else ga[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:i,name:s}=this,r=[];for(let c=0;c<i.length;c++)(i[c]===null||p2(i[c]))&&r.push(c);r.length&&T2(i,r,s)}measureInitialState(){const{element:i,unresolvedKeyframes:s,name:r}=this;if(!i||!i.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ga[r](i.measureViewportBox(),window.getComputedStyle(i.current)),s[0]=this.measuredOrigin;const c=s[s.length-1];c!==void 0&&i.getValue(r,c).jump(c,!1)}measureEndState(){var p;const{element:i,name:s,unresolvedKeyframes:r}=this;if(!i||!i.current)return;const c=i.getValue(s);c&&c.jump(this.measuredOrigin,!1);const f=r.length-1,d=r[f];r[f]=ga[s](i.measureViewportBox(),window.getComputedStyle(i.current)),d!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=d),(p=this.removedTransforms)!=null&&p.length&&this.removedTransforms.forEach(([m,h])=>{i.getValue(m).set(h)}),this.resolveNoneKeyframes()}}function E2(a,i,s){if(a instanceof EventTarget)return[a];if(typeof a=="string"){let r=document;const c=(s==null?void 0:s[a])??r.querySelectorAll(a);return c?Array.from(c):[]}return Array.from(a)}const G0=(a,i)=>i&&typeof a=="number"?i.transform(a):a,Yp=30,M2=a=>!isNaN(parseFloat(a));class w2{constructor(i,s={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,c=!0)=>{var d,p;const f=de.now();if(this.updatedAt!==f&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&((d=this.events.change)==null||d.notify(this.current),this.dependents))for(const m of this.dependents)m.dirty();c&&((p=this.events.renderRequest)==null||p.notify(this.current))},this.hasAnimated=!1,this.setCurrent(i),this.owner=s.owner}setCurrent(i){this.current=i,this.updatedAt=de.now(),this.canTrackVelocity===null&&i!==void 0&&(this.canTrackVelocity=M2(this.current))}setPrevFrameValue(i=this.current){this.prevFrameValue=i,this.prevUpdatedAt=this.updatedAt}onChange(i){return this.on("change",i)}on(i,s){this.events[i]||(this.events[i]=new Xc);const r=this.events[i].add(s);return i==="change"?()=>{r(),Ot.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const i in this.events)this.events[i].clear()}attach(i,s){this.passiveEffect=i,this.stopPassiveEffect=s}set(i,s=!0){!s||!this.passiveEffect?this.updateAndNotify(i,s):this.passiveEffect(i,this.updateAndNotify)}setWithVelocity(i,s,r){this.set(s),this.prev=void 0,this.prevFrameValue=i,this.prevUpdatedAt=this.updatedAt-r}jump(i,s=!0){this.updateAndNotify(i),this.prev=i,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var i;(i=this.events.change)==null||i.notify(this.current)}addDependent(i){this.dependents||(this.dependents=new Set),this.dependents.add(i)}removeDependent(i){this.dependents&&this.dependents.delete(i)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const i=de.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||i-this.updatedAt>Yp)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,Yp);return r0(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(i){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=i(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var i,s;(i=this.dependents)==null||i.clear(),(s=this.events.destroy)==null||s.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ui(a,i){return new w2(a,i)}const{schedule:sf}=v0(queueMicrotask,!1),Le={x:!1,y:!1};function X0(){return Le.x||Le.y}function D2(a){return a==="x"||a==="y"?Le[a]?null:(Le[a]=!0,()=>{Le[a]=!1}):Le.x||Le.y?null:(Le.x=Le.y=!0,()=>{Le.x=Le.y=!1})}function K0(a,i){const s=E2(a),r=new AbortController,c={passive:!0,...i,signal:r.signal};return[s,c,()=>r.abort()]}function Gp(a){return!(a.pointerType==="touch"||X0())}function R2(a,i,s={}){const[r,c,f]=K0(a,s),d=p=>{if(!Gp(p))return;const{target:m}=p,h=i(m,p);if(typeof h!="function"||!m)return;const g=x=>{Gp(x)&&(h(x),m.removeEventListener("pointerleave",g))};m.addEventListener("pointerleave",g,c)};return r.forEach(p=>{p.addEventListener("pointerenter",d,c)}),f}const Z0=(a,i)=>i?a===i?!0:Z0(a,i.parentElement):!1,rf=a=>a.pointerType==="mouse"?typeof a.button!="number"||a.button<=0:a.isPrimary!==!1,C2=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function N2(a){return C2.has(a.tagName)||a.tabIndex!==-1}const ur=new WeakSet;function Xp(a){return i=>{i.key==="Enter"&&a(i)}}function tc(a,i){a.dispatchEvent(new PointerEvent("pointer"+i,{isPrimary:!0,bubbles:!0}))}const j2=(a,i)=>{const s=a.currentTarget;if(!s)return;const r=Xp(()=>{if(ur.has(s))return;tc(s,"down");const c=Xp(()=>{tc(s,"up")}),f=()=>tc(s,"cancel");s.addEventListener("keyup",c,i),s.addEventListener("blur",f,i)});s.addEventListener("keydown",r,i),s.addEventListener("blur",()=>s.removeEventListener("keydown",r),i)};function Kp(a){return rf(a)&&!X0()}function O2(a,i,s={}){const[r,c,f]=K0(a,s),d=p=>{const m=p.currentTarget;if(!Kp(p))return;ur.add(m);const h=i(m,p),g=(M,D)=>{window.removeEventListener("pointerup",x),window.removeEventListener("pointercancel",b),ur.has(m)&&ur.delete(m),Kp(M)&&typeof h=="function"&&h(M,{success:D})},x=M=>{g(M,m===window||m===document||s.useGlobalTarget||Z0(m,M.target))},b=M=>{g(M,!1)};window.addEventListener("pointerup",x,c),window.addEventListener("pointercancel",b,c)};return r.forEach(p=>{(s.useGlobalTarget?window:p).addEventListener("pointerdown",d,c),U0(p)&&(p.addEventListener("focus",h=>j2(h,c)),!N2(p)&&!p.hasAttribute("tabindex")&&(p.tabIndex=0))}),f}function Q0(a){return l0(a)&&"ownerSVGElement"in a}function V2(a){return Q0(a)&&a.tagName==="svg"}const ie=a=>!!(a&&a.getVelocity),z2=[...k0,qt,Zn],_2=a=>z2.find(H0(a)),P0=C.createContext({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"});function U2(a=!0){const i=C.useContext(Hc);if(i===null)return[!0,null];const{isPresent:s,onExitComplete:r,register:c}=i,f=C.useId();C.useEffect(()=>{if(a)return c(f)},[a]);const d=C.useCallback(()=>a&&r&&r(f),[f,r,a]);return!s&&r?[!1,d]:[!0]}const $0=C.createContext({strict:!1}),Zp={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ci={};for(const a in Zp)ci[a]={isEnabled:i=>Zp[a].some(s=>!!i[s])};function B2(a){for(const i in a)ci[i]={...ci[i],...a[i]}}const L2=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function gr(a){return a.startsWith("while")||a.startsWith("drag")&&a!=="draggable"||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||L2.has(a)}let J0=a=>!gr(a);function H2(a){typeof a=="function"&&(J0=i=>i.startsWith("on")?!gr(i):a(i))}try{H2(require("@emotion/is-prop-valid").default)}catch{}function k2(a,i,s){const r={};for(const c in a)c==="values"&&typeof a.values=="object"||(J0(c)||s===!0&&gr(c)||!i&&!gr(c)||a.draggable&&c.startsWith("onDrag"))&&(r[c]=a[c]);return r}function q2(a){if(typeof Proxy>"u")return a;const i=new Map,s=(...r)=>a(...r);return new Proxy(s,{get:(r,c)=>c==="create"?a:(i.has(c)||i.set(c,a(c)),i.get(c))})}const Tr=C.createContext({});function Ar(a){return a!==null&&typeof a=="object"&&typeof a.start=="function"}function Nl(a){return typeof a=="string"||Array.isArray(a)}const of=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],uf=["initial",...of];function Er(a){return Ar(a.animate)||uf.some(i=>Nl(a[i]))}function F0(a){return!!(Er(a)||a.variants)}function Y2(a,i){if(Er(a)){const{initial:s,animate:r}=a;return{initial:s===!1||Nl(s)?s:void 0,animate:Nl(r)?r:void 0}}return a.inherit!==!1?i:{}}function G2(a){const{initial:i,animate:s}=Y2(a,C.useContext(Tr));return C.useMemo(()=>({initial:i,animate:s}),[Qp(i),Qp(s)])}function Qp(a){return Array.isArray(a)?a.join(" "):a}const X2=Symbol.for("motionComponentSymbol");function li(a){return a&&typeof a=="object"&&Object.prototype.hasOwnProperty.call(a,"current")}function K2(a,i,s){return C.useCallback(r=>{r&&a.onMount&&a.onMount(r),i&&(r?i.mount(r):i.unmount()),s&&(typeof s=="function"?s(r):li(s)&&(s.current=r))},[i])}const cf=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Z2="framerAppearId",W0="data-"+cf(Z2),I0=C.createContext({});function Q2(a,i,s,r,c){var z,B;const{visualElement:f}=C.useContext(Tr),d=C.useContext($0),p=C.useContext(Hc),m=C.useContext(P0).reducedMotion,h=C.useRef(null);r=r||d.renderer,!h.current&&r&&(h.current=r(a,{visualState:i,parent:f,props:s,presenceContext:p,blockInitialAnimation:p?p.initial===!1:!1,reducedMotionConfig:m}));const g=h.current,x=C.useContext(I0);g&&!g.projection&&c&&(g.type==="html"||g.type==="svg")&&P2(h.current,s,c,x);const b=C.useRef(!1);C.useInsertionEffect(()=>{g&&b.current&&g.update(s,p)});const M=s[W0],D=C.useRef(!!M&&!((z=window.MotionHandoffIsComplete)!=null&&z.call(window,M))&&((B=window.MotionHasOptimisedAnimation)==null?void 0:B.call(window,M)));return Hb(()=>{g&&(b.current=!0,window.MotionIsMounted=!0,g.updateFeatures(),sf.render(g.render),D.current&&g.animationState&&g.animationState.animateChanges())}),C.useEffect(()=>{g&&(!D.current&&g.animationState&&g.animationState.animateChanges(),D.current&&(queueMicrotask(()=>{var V;(V=window.MotionHandoffMarkAsComplete)==null||V.call(window,M)}),D.current=!1))}),g}function P2(a,i,s,r){const{layoutId:c,layout:f,drag:d,dragConstraints:p,layoutScroll:m,layoutRoot:h,layoutCrossfade:g}=i;a.projection=new s(a.latestValues,i["data-framer-portal-id"]?void 0:tg(a.parent)),a.projection.setOptions({layoutId:c,layout:f,alwaysMeasureLayout:!!d||p&&li(p),visualElement:a,animationType:typeof f=="string"?f:"both",initialPromotionConfig:r,crossfade:g,layoutScroll:m,layoutRoot:h})}function tg(a){if(a)return a.options.allowProjection!==!1?a.projection:tg(a.parent)}function $2({preloadedFeatures:a,createVisualElement:i,useRender:s,useVisualState:r,Component:c}){a&&B2(a);function f(p,m){let h;const g={...C.useContext(P0),...p,layoutId:J2(p)},{isStatic:x}=g,b=G2(p),M=r(p,x);if(!x&&Lc){F2();const D=W2(g);h=D.MeasureLayout,b.visualElement=Q2(c,M,g,i,D.ProjectionNode)}return S.jsxs(Tr.Provider,{value:b,children:[h&&b.visualElement?S.jsx(h,{visualElement:b.visualElement,...g}):null,s(c,p,K2(M,b.visualElement,m),M,x,b.visualElement)]})}f.displayName=`motion.${typeof c=="string"?c:`create(${c.displayName??c.name??""})`}`;const d=C.forwardRef(f);return d[X2]=c,d}function J2({layoutId:a}){const i=C.useContext(a0).id;return i&&a!==void 0?i+"-"+a:a}function F2(a,i){C.useContext($0).strict}function W2(a){const{drag:i,layout:s}=ci;if(!i&&!s)return{};const r={...i,...s};return{MeasureLayout:i!=null&&i.isEnabled(a)||s!=null&&s.isEnabled(a)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const jl={};function I2(a){for(const i in a)jl[i]=a[i],Qc(i)&&(jl[i].isCSSVariable=!0)}function eg(a,{layout:i,layoutId:s}){return pi.has(a)||a.startsWith("origin")||(i||s!==void 0)&&(!!jl[a]||a==="opacity")}const tT={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},eT=mi.length;function nT(a,i,s){let r="",c=!0;for(let f=0;f<eT;f++){const d=mi[f],p=a[d];if(p===void 0)continue;let m=!0;if(typeof p=="number"?m=p===(d.startsWith("scale")?1:0):m=parseFloat(p)===0,!m||s){const h=G0(p,lf[d]);if(!m){c=!1;const g=tT[d]||d;r+=`${g}(${h}) `}s&&(i[d]=h)}}return r=r.trim(),s?r=s(i,c?"":r):c&&(r="none"),r}function ff(a,i,s){const{style:r,vars:c,transformOrigin:f}=a;let d=!1,p=!1;for(const m in i){const h=i[m];if(pi.has(m)){d=!0;continue}else if(Qc(m)){c[m]=h;continue}else{const g=G0(h,lf[m]);m.startsWith("origin")?(p=!0,f[m]=g):r[m]=g}}if(i.transform||(d||s?r.transform=nT(i,a.transform,s):r.transform&&(r.transform="none")),p){const{originX:m="50%",originY:h="50%",originZ:g=0}=f;r.transformOrigin=`${m} ${h} ${g}`}}const df=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ng(a,i,s){for(const r in i)!ie(i[r])&&!eg(r,s)&&(a[r]=i[r])}function aT({transformTemplate:a},i){return C.useMemo(()=>{const s=df();return ff(s,i,a),Object.assign({},s.vars,s.style)},[i])}function iT(a,i){const s=a.style||{},r={};return ng(r,s,a),Object.assign(r,aT(a,i)),r}function lT(a,i){const s={},r=iT(a,i);return a.drag&&a.dragListener!==!1&&(s.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=a.drag===!0?"none":`pan-${a.drag==="x"?"y":"x"}`),a.tabIndex===void 0&&(a.onTap||a.onTapStart||a.whileTap)&&(s.tabIndex=0),s.style=r,s}const sT={offset:"stroke-dashoffset",array:"stroke-dasharray"},rT={offset:"strokeDashoffset",array:"strokeDasharray"};function oT(a,i,s=1,r=0,c=!0){a.pathLength=1;const f=c?sT:rT;a[f.offset]=nt.transform(-r);const d=nt.transform(i),p=nt.transform(s);a[f.array]=`${d} ${p}`}function ag(a,{attrX:i,attrY:s,attrScale:r,pathLength:c,pathSpacing:f=1,pathOffset:d=0,...p},m,h,g){if(ff(a,p,h),m){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};const{attrs:x,style:b}=a;x.transform&&(b.transform=x.transform,delete x.transform),(b.transform||x.transformOrigin)&&(b.transformOrigin=x.transformOrigin??"50% 50%",delete x.transformOrigin),b.transform&&(b.transformBox=(g==null?void 0:g.transformBox)??"fill-box",delete x.transformBox),i!==void 0&&(x.x=i),s!==void 0&&(x.y=s),r!==void 0&&(x.scale=r),c!==void 0&&oT(x,c,f,d,!1)}const ig=()=>({...df(),attrs:{}}),lg=a=>typeof a=="string"&&a.toLowerCase()==="svg";function uT(a,i,s,r){const c=C.useMemo(()=>{const f=ig();return ag(f,i,lg(r),a.transformTemplate,a.style),{...f.attrs,style:{...f.style}}},[i]);if(a.style){const f={};ng(f,a.style,a),c.style={...f,...c.style}}return c}const cT=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function hf(a){return typeof a!="string"||a.includes("-")?!1:!!(cT.indexOf(a)>-1||/[A-Z]/u.test(a))}function fT(a=!1){return(s,r,c,{latestValues:f},d)=>{const m=(hf(s)?uT:lT)(r,f,d,s),h=k2(r,typeof s=="string",a),g=s!==C.Fragment?{...h,...m,ref:c}:{},{children:x}=r,b=C.useMemo(()=>ie(x)?x.get():x,[x]);return C.createElement(s,{...g,children:b})}}function Pp(a){const i=[{},{}];return a==null||a.values.forEach((s,r)=>{i[0][r]=s.get(),i[1][r]=s.getVelocity()}),i}function mf(a,i,s,r){if(typeof i=="function"){const[c,f]=Pp(r);i=i(s!==void 0?s:a.custom,c,f)}if(typeof i=="string"&&(i=a.variants&&a.variants[i]),typeof i=="function"){const[c,f]=Pp(r);i=i(s!==void 0?s:a.custom,c,f)}return i}function cr(a){return ie(a)?a.get():a}function dT({scrapeMotionValuesFromProps:a,createRenderState:i},s,r,c){return{latestValues:hT(s,r,c,a),renderState:i()}}const sg=a=>(i,s)=>{const r=C.useContext(Tr),c=C.useContext(Hc),f=()=>dT(a,i,r,c);return s?f():Lb(f)};function hT(a,i,s,r){const c={},f=r(a,{});for(const b in f)c[b]=cr(f[b]);let{initial:d,animate:p}=a;const m=Er(a),h=F0(a);i&&h&&!m&&a.inherit!==!1&&(d===void 0&&(d=i.initial),p===void 0&&(p=i.animate));let g=s?s.initial===!1:!1;g=g||d===!1;const x=g?p:d;if(x&&typeof x!="boolean"&&!Ar(x)){const b=Array.isArray(x)?x:[x];for(let M=0;M<b.length;M++){const D=mf(a,b[M]);if(D){const{transitionEnd:z,transition:B,...V}=D;for(const q in V){let k=V[q];if(Array.isArray(k)){const P=g?k.length-1:0;k=k[P]}k!==null&&(c[q]=k)}for(const q in z)c[q]=z[q]}}}return c}function pf(a,i,s){var f;const{style:r}=a,c={};for(const d in r)(ie(r[d])||i.style&&ie(i.style[d])||eg(d,a)||((f=s==null?void 0:s.getValue(d))==null?void 0:f.liveStyle)!==void 0)&&(c[d]=r[d]);return c}const mT={useVisualState:sg({scrapeMotionValuesFromProps:pf,createRenderState:df})};function rg(a,i,s){const r=pf(a,i,s);for(const c in a)if(ie(a[c])||ie(i[c])){const f=mi.indexOf(c)!==-1?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c;r[f]=a[c]}return r}const pT={useVisualState:sg({scrapeMotionValuesFromProps:rg,createRenderState:ig})};function yT(a,i){return function(r,{forwardMotionProps:c}={forwardMotionProps:!1}){const d={...hf(r)?pT:mT,preloadedFeatures:a,useRender:fT(c),createVisualElement:i,Component:r};return $2(d)}}function Ol(a,i,s){const r=a.getProps();return mf(r,i,s!==void 0?s:r.custom,a)}const Tc=a=>Array.isArray(a);function gT(a,i,s){a.hasValue(i)?a.getValue(i).set(s):a.addValue(i,ui(s))}function vT(a){return Tc(a)?a[a.length-1]||0:a}function xT(a,i){const s=Ol(a,i);let{transitionEnd:r={},transition:c={},...f}=s||{};f={...f,...r};for(const d in f){const p=vT(f[d]);gT(a,d,p)}}function bT(a){return!!(ie(a)&&a.add)}function Ac(a,i){const s=a.getValue("willChange");if(bT(s))return s.add(i);if(!s&&gn.WillChange){const r=new gn.WillChange("auto");a.addValue("willChange",r),r.add(i)}}function og(a){return a.props[W0]}const ST=a=>a!==null;function TT(a,{repeat:i,repeatType:s="loop"},r){const c=a.filter(ST),f=i&&s!=="loop"&&i%2===1?0:c.length-1;return c[f]}const AT={type:"spring",stiffness:500,damping:25,restSpeed:10},ET=a=>({type:"spring",stiffness:550,damping:a===0?2*Math.sqrt(550):30,restSpeed:10}),MT={type:"keyframes",duration:.8},wT={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},DT=(a,{keyframes:i})=>i.length>2?MT:pi.has(a)?a.startsWith("scale")?ET(i[1]):AT:wT;function RT({when:a,delay:i,delayChildren:s,staggerChildren:r,staggerDirection:c,repeat:f,repeatType:d,repeatDelay:p,from:m,elapsed:h,...g}){return!!Object.keys(g).length}const yf=(a,i,s,r={},c,f)=>d=>{const p=af(r,a)||{},m=p.delay||r.delay||0;let{elapsed:h=0}=r;h=h-Ze(m);const g={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:i.getVelocity(),...p,delay:-h,onUpdate:b=>{i.set(b),p.onUpdate&&p.onUpdate(b)},onComplete:()=>{d(),p.onComplete&&p.onComplete()},name:a,motionValue:i,element:f?void 0:c};RT(p)||Object.assign(g,DT(a,g)),g.duration&&(g.duration=Ze(g.duration)),g.repeatDelay&&(g.repeatDelay=Ze(g.repeatDelay)),g.from!==void 0&&(g.keyframes[0]=g.from);let x=!1;if((g.type===!1||g.duration===0&&!g.repeatDelay)&&(g.duration=0,g.delay===0&&(x=!0)),(gn.instantAnimations||gn.skipAnimations)&&(x=!0,g.duration=0,g.delay=0),g.allowFlatten=!p.type&&!p.ease,x&&!f&&i.get()!==void 0){const b=TT(g.keyframes,p);if(b!==void 0){Ot.update(()=>{g.onUpdate(b),g.onComplete()});return}}return p.isSync?new ef(g):new f2(g)};function CT({protectedKeys:a,needsAnimating:i},s){const r=a.hasOwnProperty(s)&&i[s]!==!0;return i[s]=!1,r}function ug(a,i,{delay:s=0,transitionOverride:r,type:c}={}){let{transition:f=a.getDefaultTransition(),transitionEnd:d,...p}=i;r&&(f=r);const m=[],h=c&&a.animationState&&a.animationState.getState()[c];for(const g in p){const x=a.getValue(g,a.latestValues[g]??null),b=p[g];if(b===void 0||h&&CT(h,g))continue;const M={delay:s,...af(f||{},g)},D=x.get();if(D!==void 0&&!x.isAnimating&&!Array.isArray(b)&&b===D&&!M.velocity)continue;let z=!1;if(window.MotionHandoffAnimation){const V=og(a);if(V){const q=window.MotionHandoffAnimation(V,g,Ot);q!==null&&(M.startTime=q,z=!0)}}Ac(a,g),x.start(yf(g,x,b,a.shouldReduceMotion&&L0.has(g)?{type:!1}:M,a,z));const B=x.animation;B&&m.push(B)}return d&&Promise.all(m).then(()=>{Ot.update(()=>{d&&xT(a,d)})}),m}function Ec(a,i,s={}){var m;const r=Ol(a,i,s.type==="exit"?(m=a.presenceContext)==null?void 0:m.custom:void 0);let{transition:c=a.getDefaultTransition()||{}}=r||{};s.transitionOverride&&(c=s.transitionOverride);const f=r?()=>Promise.all(ug(a,r,s)):()=>Promise.resolve(),d=a.variantChildren&&a.variantChildren.size?(h=0)=>{const{delayChildren:g=0,staggerChildren:x,staggerDirection:b}=c;return NT(a,i,g+h,x,b,s)}:()=>Promise.resolve(),{when:p}=c;if(p){const[h,g]=p==="beforeChildren"?[f,d]:[d,f];return h().then(()=>g())}else return Promise.all([f(),d(s.delay)])}function NT(a,i,s=0,r=0,c=1,f){const d=[],p=(a.variantChildren.size-1)*r,m=c===1?(h=0)=>h*r:(h=0)=>p-h*r;return Array.from(a.variantChildren).sort(jT).forEach((h,g)=>{h.notify("AnimationStart",i),d.push(Ec(h,i,{...f,delay:s+m(g)}).then(()=>h.notify("AnimationComplete",i)))}),Promise.all(d)}function jT(a,i){return a.sortNodePosition(i)}function OT(a,i,s={}){a.notify("AnimationStart",i);let r;if(Array.isArray(i)){const c=i.map(f=>Ec(a,f,s));r=Promise.all(c)}else if(typeof i=="string")r=Ec(a,i,s);else{const c=typeof i=="function"?Ol(a,i,s.custom):i;r=Promise.all(ug(a,c,s))}return r.then(()=>{a.notify("AnimationComplete",i)})}function cg(a,i){if(!Array.isArray(i))return!1;const s=i.length;if(s!==a.length)return!1;for(let r=0;r<s;r++)if(i[r]!==a[r])return!1;return!0}const VT=uf.length;function fg(a){if(!a)return;if(!a.isControllingVariants){const s=a.parent?fg(a.parent)||{}:{};return a.props.initial!==void 0&&(s.initial=a.props.initial),s}const i={};for(let s=0;s<VT;s++){const r=uf[s],c=a.props[r];(Nl(c)||c===!1)&&(i[r]=c)}return i}const zT=[...of].reverse(),_T=of.length;function UT(a){return i=>Promise.all(i.map(({animation:s,options:r})=>OT(a,s,r)))}function BT(a){let i=UT(a),s=$p(),r=!0;const c=m=>(h,g)=>{var b;const x=Ol(a,g,m==="exit"?(b=a.presenceContext)==null?void 0:b.custom:void 0);if(x){const{transition:M,transitionEnd:D,...z}=x;h={...h,...z,...D}}return h};function f(m){i=m(a)}function d(m){const{props:h}=a,g=fg(a.parent)||{},x=[],b=new Set;let M={},D=1/0;for(let B=0;B<_T;B++){const V=zT[B],q=s[V],k=h[V]!==void 0?h[V]:g[V],P=Nl(k),G=V===m?q.isActive:null;G===!1&&(D=B);let at=k===g[V]&&k!==h[V]&&P;if(at&&r&&a.manuallyAnimateOnMount&&(at=!1),q.protectedKeys={...M},!q.isActive&&G===null||!k&&!q.prevProp||Ar(k)||typeof k=="boolean")continue;const lt=LT(q.prevProp,k);let J=lt||V===m&&q.isActive&&!at&&P||B>D&&P,rt=!1;const bt=Array.isArray(k)?k:[k];let Yt=bt.reduce(c(V),{});G===!1&&(Yt={});const{prevResolvedValues:Gt={}}=q,Fe={...Gt,...Yt},He=X=>{J=!0,b.has(X)&&(rt=!0,b.delete(X)),q.needsAnimating[X]=!0;const $=a.getValue(X);$&&($.liveStyle=!1)};for(const X in Fe){const $=Yt[X],yt=Gt[X];if(M.hasOwnProperty(X))continue;let A=!1;Tc($)&&Tc(yt)?A=!cg($,yt):A=$!==yt,A?$!=null?He(X):b.add(X):$!==void 0&&b.has(X)?He(X):q.protectedKeys[X]=!0}q.prevProp=k,q.prevResolvedValues=Yt,q.isActive&&(M={...M,...Yt}),r&&a.blockInitialAnimation&&(J=!1),J&&(!(at&&lt)||rt)&&x.push(...bt.map(X=>({animation:X,options:{type:V}})))}if(b.size){const B={};if(typeof h.initial!="boolean"){const V=Ol(a,Array.isArray(h.initial)?h.initial[0]:h.initial);V&&V.transition&&(B.transition=V.transition)}b.forEach(V=>{const q=a.getBaseTarget(V),k=a.getValue(V);k&&(k.liveStyle=!0),B[V]=q??null}),x.push({animation:B})}let z=!!x.length;return r&&(h.initial===!1||h.initial===h.animate)&&!a.manuallyAnimateOnMount&&(z=!1),r=!1,z?i(x):Promise.resolve()}function p(m,h){var x;if(s[m].isActive===h)return Promise.resolve();(x=a.variantChildren)==null||x.forEach(b=>{var M;return(M=b.animationState)==null?void 0:M.setActive(m,h)}),s[m].isActive=h;const g=d(m);for(const b in s)s[b].protectedKeys={};return g}return{animateChanges:d,setActive:p,setAnimateFunction:f,getState:()=>s,reset:()=>{s=$p(),r=!0}}}function LT(a,i){return typeof i=="string"?i!==a:Array.isArray(i)?!cg(i,a):!1}function ha(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function $p(){return{animate:ha(!0),whileInView:ha(),whileHover:ha(),whileTap:ha(),whileDrag:ha(),whileFocus:ha(),exit:ha()}}class Pn{constructor(i){this.isMounted=!1,this.node=i}update(){}}class HT extends Pn{constructor(i){super(i),i.animationState||(i.animationState=BT(i))}updateAnimationControlsSubscription(){const{animate:i}=this.node.getProps();Ar(i)&&(this.unmountControls=i.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:i}=this.node.getProps(),{animate:s}=this.node.prevProps||{};i!==s&&this.updateAnimationControlsSubscription()}unmount(){var i;this.node.animationState.reset(),(i=this.unmountControls)==null||i.call(this)}}let kT=0;class qT extends Pn{constructor(){super(...arguments),this.id=kT++}update(){if(!this.node.presenceContext)return;const{isPresent:i,onExitComplete:s}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||i===r)return;const c=this.node.animationState.setActive("exit",!i);s&&!i&&c.then(()=>{s(this.id)})}mount(){const{register:i,onExitComplete:s}=this.node.presenceContext||{};s&&s(this.id),i&&(this.unmount=i(this.id))}unmount(){}}const YT={animation:{Feature:HT},exit:{Feature:qT}};function Vl(a,i,s,r={passive:!0}){return a.addEventListener(i,s,r),()=>a.removeEventListener(i,s)}function kl(a){return{point:{x:a.pageX,y:a.pageY}}}const GT=a=>i=>rf(i)&&a(i,kl(i));function Tl(a,i,s,r){return Vl(a,i,GT(s),r)}function dg({top:a,left:i,right:s,bottom:r}){return{x:{min:i,max:s},y:{min:a,max:r}}}function XT({x:a,y:i}){return{top:i.min,right:a.max,bottom:i.max,left:a.min}}function KT(a,i){if(!i)return a;const s=i({x:a.left,y:a.top}),r=i({x:a.right,y:a.bottom});return{top:s.y,left:s.x,bottom:r.y,right:r.x}}const hg=1e-4,ZT=1-hg,QT=1+hg,mg=.01,PT=0-mg,$T=0+mg;function se(a){return a.max-a.min}function JT(a,i,s){return Math.abs(a-i)<=s}function Jp(a,i,s,r=.5){a.origin=r,a.originPoint=Nt(i.min,i.max,a.origin),a.scale=se(s)/se(i),a.translate=Nt(s.min,s.max,a.origin)-a.originPoint,(a.scale>=ZT&&a.scale<=QT||isNaN(a.scale))&&(a.scale=1),(a.translate>=PT&&a.translate<=$T||isNaN(a.translate))&&(a.translate=0)}function Al(a,i,s,r){Jp(a.x,i.x,s.x,r?r.originX:void 0),Jp(a.y,i.y,s.y,r?r.originY:void 0)}function Fp(a,i,s){a.min=s.min+i.min,a.max=a.min+se(i)}function FT(a,i,s){Fp(a.x,i.x,s.x),Fp(a.y,i.y,s.y)}function Wp(a,i,s){a.min=i.min-s.min,a.max=a.min+se(i)}function El(a,i,s){Wp(a.x,i.x,s.x),Wp(a.y,i.y,s.y)}const Ip=()=>({translate:0,scale:1,origin:0,originPoint:0}),si=()=>({x:Ip(),y:Ip()}),ty=()=>({min:0,max:0}),Lt=()=>({x:ty(),y:ty()});function Ve(a){return[a("x"),a("y")]}function ec(a){return a===void 0||a===1}function Mc({scale:a,scaleX:i,scaleY:s}){return!ec(a)||!ec(i)||!ec(s)}function ma(a){return Mc(a)||pg(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function pg(a){return ey(a.x)||ey(a.y)}function ey(a){return a&&a!=="0%"}function vr(a,i,s){const r=a-s,c=i*r;return s+c}function ny(a,i,s,r,c){return c!==void 0&&(a=vr(a,c,r)),vr(a,s,r)+i}function wc(a,i=0,s=1,r,c){a.min=ny(a.min,i,s,r,c),a.max=ny(a.max,i,s,r,c)}function yg(a,{x:i,y:s}){wc(a.x,i.translate,i.scale,i.originPoint),wc(a.y,s.translate,s.scale,s.originPoint)}const ay=.999999999999,iy=1.0000000000001;function WT(a,i,s,r=!1){const c=s.length;if(!c)return;i.x=i.y=1;let f,d;for(let p=0;p<c;p++){f=s[p],d=f.projectionDelta;const{visualElement:m}=f.options;m&&m.props.style&&m.props.style.display==="contents"||(r&&f.options.layoutScroll&&f.scroll&&f!==f.root&&oi(a,{x:-f.scroll.offset.x,y:-f.scroll.offset.y}),d&&(i.x*=d.x.scale,i.y*=d.y.scale,yg(a,d)),r&&ma(f.latestValues)&&oi(a,f.latestValues))}i.x<iy&&i.x>ay&&(i.x=1),i.y<iy&&i.y>ay&&(i.y=1)}function ri(a,i){a.min=a.min+i,a.max=a.max+i}function ly(a,i,s,r,c=.5){const f=Nt(a.min,a.max,c);wc(a,i,s,f,r)}function oi(a,i){ly(a.x,i.x,i.scaleX,i.scale,i.originX),ly(a.y,i.y,i.scaleY,i.scale,i.originY)}function gg(a,i){return dg(KT(a.getBoundingClientRect(),i))}function IT(a,i,s){const r=gg(a,s),{scroll:c}=i;return c&&(ri(r.x,c.offset.x),ri(r.y,c.offset.y)),r}const vg=({current:a})=>a?a.ownerDocument.defaultView:null,sy=(a,i)=>Math.abs(a-i);function tA(a,i){const s=sy(a.x,i.x),r=sy(a.y,i.y);return Math.sqrt(s**2+r**2)}class xg{constructor(i,s,{transformPagePoint:r,contextWindow:c,dragSnapToOrigin:f=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=ac(this.lastMoveEventInfo,this.history),b=this.startEvent!==null,M=tA(x.offset,{x:0,y:0})>=3;if(!b&&!M)return;const{point:D}=x,{timestamp:z}=te;this.history.push({...D,timestamp:z});const{onStart:B,onMove:V}=this.handlers;b||(B&&B(this.lastMoveEvent,x),this.startEvent=this.lastMoveEvent),V&&V(this.lastMoveEvent,x)},this.handlePointerMove=(x,b)=>{this.lastMoveEvent=x,this.lastMoveEventInfo=nc(b,this.transformPagePoint),Ot.update(this.updatePoint,!0)},this.handlePointerUp=(x,b)=>{this.end();const{onEnd:M,onSessionEnd:D,resumeAnimation:z}=this.handlers;if(this.dragSnapToOrigin&&z&&z(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const B=ac(x.type==="pointercancel"?this.lastMoveEventInfo:nc(b,this.transformPagePoint),this.history);this.startEvent&&M&&M(x,B),D&&D(x,B)},!rf(i))return;this.dragSnapToOrigin=f,this.handlers=s,this.transformPagePoint=r,this.contextWindow=c||window;const d=kl(i),p=nc(d,this.transformPagePoint),{point:m}=p,{timestamp:h}=te;this.history=[{...m,timestamp:h}];const{onSessionStart:g}=s;g&&g(i,ac(p,this.history)),this.removeListeners=Bl(Tl(this.contextWindow,"pointermove",this.handlePointerMove),Tl(this.contextWindow,"pointerup",this.handlePointerUp),Tl(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(i){this.handlers=i}end(){this.removeListeners&&this.removeListeners(),Kn(this.updatePoint)}}function nc(a,i){return i?{point:i(a.point)}:a}function ry(a,i){return{x:a.x-i.x,y:a.y-i.y}}function ac({point:a},i){return{point:a,delta:ry(a,bg(i)),offset:ry(a,eA(i)),velocity:nA(i,.1)}}function eA(a){return a[0]}function bg(a){return a[a.length-1]}function nA(a,i){if(a.length<2)return{x:0,y:0};let s=a.length-1,r=null;const c=bg(a);for(;s>=0&&(r=a[s],!(c.timestamp-r.timestamp>Ze(i)));)s--;if(!r)return{x:0,y:0};const f=Qe(c.timestamp-r.timestamp);if(f===0)return{x:0,y:0};const d={x:(c.x-r.x)/f,y:(c.y-r.y)/f};return d.x===1/0&&(d.x=0),d.y===1/0&&(d.y=0),d}function aA(a,{min:i,max:s},r){return i!==void 0&&a<i?a=r?Nt(i,a,r.min):Math.max(a,i):s!==void 0&&a>s&&(a=r?Nt(s,a,r.max):Math.min(a,s)),a}function oy(a,i,s){return{min:i!==void 0?a.min+i:void 0,max:s!==void 0?a.max+s-(a.max-a.min):void 0}}function iA(a,{top:i,left:s,bottom:r,right:c}){return{x:oy(a.x,s,c),y:oy(a.y,i,r)}}function uy(a,i){let s=i.min-a.min,r=i.max-a.max;return i.max-i.min<a.max-a.min&&([s,r]=[r,s]),{min:s,max:r}}function lA(a,i){return{x:uy(a.x,i.x),y:uy(a.y,i.y)}}function sA(a,i){let s=.5;const r=se(a),c=se(i);return c>r?s=Dl(i.min,i.max-r,a.min):r>c&&(s=Dl(a.min,a.max-c,i.min)),yn(0,1,s)}function rA(a,i){const s={};return i.min!==void 0&&(s.min=i.min-a.min),i.max!==void 0&&(s.max=i.max-a.min),s}const Dc=.35;function oA(a=Dc){return a===!1?a=0:a===!0&&(a=Dc),{x:cy(a,"left","right"),y:cy(a,"top","bottom")}}function cy(a,i,s){return{min:fy(a,i),max:fy(a,s)}}function fy(a,i){return typeof a=="number"?a:a[i]||0}const uA=new WeakMap;class cA{constructor(i){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Lt(),this.visualElement=i}start(i,{snapToCursor:s=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const c=g=>{const{dragSnapToOrigin:x}=this.getProps();x?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(kl(g).point)},f=(g,x)=>{const{drag:b,dragPropagation:M,onDragStart:D}=this.getProps();if(b&&!M&&(this.openDragLock&&this.openDragLock(),this.openDragLock=D2(b),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ve(B=>{let V=this.getAxisMotionValue(B).get()||0;if(Pe.test(V)){const{projection:q}=this.visualElement;if(q&&q.layout){const k=q.layout.layoutBox[B];k&&(V=se(k)*(parseFloat(V)/100))}}this.originPoint[B]=V}),D&&Ot.postRender(()=>D(g,x)),Ac(this.visualElement,"transform");const{animationState:z}=this.visualElement;z&&z.setActive("whileDrag",!0)},d=(g,x)=>{const{dragPropagation:b,dragDirectionLock:M,onDirectionLock:D,onDrag:z}=this.getProps();if(!b&&!this.openDragLock)return;const{offset:B}=x;if(M&&this.currentDirection===null){this.currentDirection=fA(B),this.currentDirection!==null&&D&&D(this.currentDirection);return}this.updateAxis("x",x.point,B),this.updateAxis("y",x.point,B),this.visualElement.render(),z&&z(g,x)},p=(g,x)=>this.stop(g,x),m=()=>Ve(g=>{var x;return this.getAnimationState(g)==="paused"&&((x=this.getAxisMotionValue(g).animation)==null?void 0:x.play())}),{dragSnapToOrigin:h}=this.getProps();this.panSession=new xg(i,{onSessionStart:c,onStart:f,onMove:d,onSessionEnd:p,resumeAnimation:m},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:h,contextWindow:vg(this.visualElement)})}stop(i,s){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:c}=s;this.startAnimation(c);const{onDragEnd:f}=this.getProps();f&&Ot.postRender(()=>f(i,s))}cancel(){this.isDragging=!1;const{projection:i,animationState:s}=this.visualElement;i&&(i.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(i,s,r){const{drag:c}=this.getProps();if(!r||!lr(i,c,this.currentDirection))return;const f=this.getAxisMotionValue(i);let d=this.originPoint[i]+r[i];this.constraints&&this.constraints[i]&&(d=aA(d,this.constraints[i],this.elastic[i])),f.set(d)}resolveConstraints(){var f;const{dragConstraints:i,dragElastic:s}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(f=this.visualElement.projection)==null?void 0:f.layout,c=this.constraints;i&&li(i)?this.constraints||(this.constraints=this.resolveRefConstraints()):i&&r?this.constraints=iA(r.layoutBox,i):this.constraints=!1,this.elastic=oA(s),c!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&Ve(d=>{this.constraints!==!1&&this.getAxisMotionValue(d)&&(this.constraints[d]=rA(r.layoutBox[d],this.constraints[d]))})}resolveRefConstraints(){const{dragConstraints:i,onMeasureDragConstraints:s}=this.getProps();if(!i||!li(i))return!1;const r=i.current,{projection:c}=this.visualElement;if(!c||!c.layout)return!1;const f=IT(r,c.root,this.visualElement.getTransformPagePoint());let d=lA(c.layout.layoutBox,f);if(s){const p=s(XT(d));this.hasMutatedConstraints=!!p,p&&(d=dg(p))}return d}startAnimation(i){const{drag:s,dragMomentum:r,dragElastic:c,dragTransition:f,dragSnapToOrigin:d,onDragTransitionEnd:p}=this.getProps(),m=this.constraints||{},h=Ve(g=>{if(!lr(g,s,this.currentDirection))return;let x=m&&m[g]||{};d&&(x={min:0,max:0});const b=c?200:1e6,M=c?40:1e7,D={type:"inertia",velocity:r?i[g]:0,bounceStiffness:b,bounceDamping:M,timeConstant:750,restDelta:1,restSpeed:10,...f,...x};return this.startAxisValueAnimation(g,D)});return Promise.all(h).then(p)}startAxisValueAnimation(i,s){const r=this.getAxisMotionValue(i);return Ac(this.visualElement,i),r.start(yf(i,r,0,s,this.visualElement,!1))}stopAnimation(){Ve(i=>this.getAxisMotionValue(i).stop())}pauseAnimation(){Ve(i=>{var s;return(s=this.getAxisMotionValue(i).animation)==null?void 0:s.pause()})}getAnimationState(i){var s;return(s=this.getAxisMotionValue(i).animation)==null?void 0:s.state}getAxisMotionValue(i){const s=`_drag${i.toUpperCase()}`,r=this.visualElement.getProps(),c=r[s];return c||this.visualElement.getValue(i,(r.initial?r.initial[i]:void 0)||0)}snapToCursor(i){Ve(s=>{const{drag:r}=this.getProps();if(!lr(s,r,this.currentDirection))return;const{projection:c}=this.visualElement,f=this.getAxisMotionValue(s);if(c&&c.layout){const{min:d,max:p}=c.layout.layoutBox[s];f.set(i[s]-Nt(d,p,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:i,dragConstraints:s}=this.getProps(),{projection:r}=this.visualElement;if(!li(s)||!r||!this.constraints)return;this.stopAnimation();const c={x:0,y:0};Ve(d=>{const p=this.getAxisMotionValue(d);if(p&&this.constraints!==!1){const m=p.get();c[d]=sA({min:m,max:m},this.constraints[d])}});const{transformTemplate:f}=this.visualElement.getProps();this.visualElement.current.style.transform=f?f({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ve(d=>{if(!lr(d,i,null))return;const p=this.getAxisMotionValue(d),{min:m,max:h}=this.constraints[d];p.set(Nt(m,h,c[d]))})}addListeners(){if(!this.visualElement.current)return;uA.set(this.visualElement,this);const i=this.visualElement.current,s=Tl(i,"pointerdown",m=>{const{drag:h,dragListener:g=!0}=this.getProps();h&&g&&this.start(m)}),r=()=>{const{dragConstraints:m}=this.getProps();li(m)&&m.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,f=c.addEventListener("measure",r);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),Ot.read(r);const d=Vl(window,"resize",()=>this.scalePositionWithinConstraints()),p=c.addEventListener("didUpdate",({delta:m,hasLayoutChanged:h})=>{this.isDragging&&h&&(Ve(g=>{const x=this.getAxisMotionValue(g);x&&(this.originPoint[g]+=m[g].translate,x.set(x.get()+m[g].translate))}),this.visualElement.render())});return()=>{d(),s(),f(),p&&p()}}getProps(){const i=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:r=!1,dragPropagation:c=!1,dragConstraints:f=!1,dragElastic:d=Dc,dragMomentum:p=!0}=i;return{...i,drag:s,dragDirectionLock:r,dragPropagation:c,dragConstraints:f,dragElastic:d,dragMomentum:p}}}function lr(a,i,s){return(i===!0||i===a)&&(s===null||s===a)}function fA(a,i=10){let s=null;return Math.abs(a.y)>i?s="y":Math.abs(a.x)>i&&(s="x"),s}class dA extends Pn{constructor(i){super(i),this.removeGroupControls=ze,this.removeListeners=ze,this.controls=new cA(i)}mount(){const{dragControls:i}=this.node.getProps();i&&(this.removeGroupControls=i.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ze}unmount(){this.removeGroupControls(),this.removeListeners()}}const dy=a=>(i,s)=>{a&&Ot.postRender(()=>a(i,s))};class hA extends Pn{constructor(){super(...arguments),this.removePointerDownListener=ze}onPointerDown(i){this.session=new xg(i,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:vg(this.node)})}createPanHandlers(){const{onPanSessionStart:i,onPanStart:s,onPan:r,onPanEnd:c}=this.node.getProps();return{onSessionStart:dy(i),onStart:dy(s),onMove:r,onEnd:(f,d)=>{delete this.session,c&&Ot.postRender(()=>c(f,d))}}}mount(){this.removePointerDownListener=Tl(this.node.current,"pointerdown",i=>this.onPointerDown(i))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const fr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function hy(a,i){return i.max===i.min?0:a/(i.max-i.min)*100}const gl={correct:(a,i)=>{if(!i.target)return a;if(typeof a=="string")if(nt.test(a))a=parseFloat(a);else return a;const s=hy(a,i.target.x),r=hy(a,i.target.y);return`${s}% ${r}%`}},mA={correct:(a,{treeScale:i,projectionDelta:s})=>{const r=a,c=Zn.parse(a);if(c.length>5)return r;const f=Zn.createTransformer(a),d=typeof c[0]!="number"?1:0,p=s.x.scale*i.x,m=s.y.scale*i.y;c[0+d]/=p,c[1+d]/=m;const h=Nt(p,m,.5);return typeof c[2+d]=="number"&&(c[2+d]/=h),typeof c[3+d]=="number"&&(c[3+d]/=h),f(c)}};class pA extends C.Component{componentDidMount(){const{visualElement:i,layoutGroup:s,switchLayoutGroup:r,layoutId:c}=this.props,{projection:f}=i;I2(yA),f&&(s.group&&s.group.add(f),r&&r.register&&c&&r.register(f),f.root.didUpdate(),f.addEventListener("animationComplete",()=>{this.safeToRemove()}),f.setOptions({...f.options,onExitComplete:()=>this.safeToRemove()})),fr.hasEverUpdated=!0}getSnapshotBeforeUpdate(i){const{layoutDependency:s,visualElement:r,drag:c,isPresent:f}=this.props,{projection:d}=r;return d&&(d.isPresent=f,c||i.layoutDependency!==s||s===void 0||i.isPresent!==f?d.willUpdate():this.safeToRemove(),i.isPresent!==f&&(f?d.promote():d.relegate()||Ot.postRender(()=>{const p=d.getStack();(!p||!p.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:i}=this.props.visualElement;i&&(i.root.didUpdate(),sf.postRender(()=>{!i.currentAnimation&&i.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:i,layoutGroup:s,switchLayoutGroup:r}=this.props,{projection:c}=i;c&&(c.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(c),r&&r.deregister&&r.deregister(c))}safeToRemove(){const{safeToRemove:i}=this.props;i&&i()}render(){return null}}function Sg(a){const[i,s]=U2(),r=C.useContext(a0);return S.jsx(pA,{...a,layoutGroup:r,switchLayoutGroup:C.useContext(I0),isPresent:i,safeToRemove:s})}const yA={borderRadius:{...gl,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:gl,borderTopRightRadius:gl,borderBottomLeftRadius:gl,borderBottomRightRadius:gl,boxShadow:mA};function gA(a,i,s){const r=ie(a)?a:ui(a);return r.start(yf("",r,i,s)),r.animation}const vA=(a,i)=>a.depth-i.depth;class xA{constructor(){this.children=[],this.isDirty=!1}add(i){kc(this.children,i),this.isDirty=!0}remove(i){qc(this.children,i),this.isDirty=!0}forEach(i){this.isDirty&&this.children.sort(vA),this.isDirty=!1,this.children.forEach(i)}}function bA(a,i){const s=de.now(),r=({timestamp:c})=>{const f=c-s;f>=i&&(Kn(r),a(f-i))};return Ot.setup(r,!0),()=>Kn(r)}const Tg=["TopLeft","TopRight","BottomLeft","BottomRight"],SA=Tg.length,my=a=>typeof a=="string"?parseFloat(a):a,py=a=>typeof a=="number"||nt.test(a);function TA(a,i,s,r,c,f){c?(a.opacity=Nt(0,s.opacity??1,AA(r)),a.opacityExit=Nt(i.opacity??1,0,EA(r))):f&&(a.opacity=Nt(i.opacity??1,s.opacity??1,r));for(let d=0;d<SA;d++){const p=`border${Tg[d]}Radius`;let m=yy(i,p),h=yy(s,p);if(m===void 0&&h===void 0)continue;m||(m=0),h||(h=0),m===0||h===0||py(m)===py(h)?(a[p]=Math.max(Nt(my(m),my(h),r),0),(Pe.test(h)||Pe.test(m))&&(a[p]+="%")):a[p]=h}(i.rotate||s.rotate)&&(a.rotate=Nt(i.rotate||0,s.rotate||0,r))}function yy(a,i){return a[i]!==void 0?a[i]:a.borderRadius}const AA=Ag(0,.5,m0),EA=Ag(.5,.95,ze);function Ag(a,i,s){return r=>r<a?0:r>i?1:s(Dl(a,i,r))}function gy(a,i){a.min=i.min,a.max=i.max}function Oe(a,i){gy(a.x,i.x),gy(a.y,i.y)}function vy(a,i){a.translate=i.translate,a.scale=i.scale,a.originPoint=i.originPoint,a.origin=i.origin}function xy(a,i,s,r,c){return a-=i,a=vr(a,1/s,r),c!==void 0&&(a=vr(a,1/c,r)),a}function MA(a,i=0,s=1,r=.5,c,f=a,d=a){if(Pe.test(i)&&(i=parseFloat(i),i=Nt(d.min,d.max,i/100)-d.min),typeof i!="number")return;let p=Nt(f.min,f.max,r);a===f&&(p-=i),a.min=xy(a.min,i,s,p,c),a.max=xy(a.max,i,s,p,c)}function by(a,i,[s,r,c],f,d){MA(a,i[s],i[r],i[c],i.scale,f,d)}const wA=["x","scaleX","originX"],DA=["y","scaleY","originY"];function Sy(a,i,s,r){by(a.x,i,wA,s?s.x:void 0,r?r.x:void 0),by(a.y,i,DA,s?s.y:void 0,r?r.y:void 0)}function Ty(a){return a.translate===0&&a.scale===1}function Eg(a){return Ty(a.x)&&Ty(a.y)}function Ay(a,i){return a.min===i.min&&a.max===i.max}function RA(a,i){return Ay(a.x,i.x)&&Ay(a.y,i.y)}function Ey(a,i){return Math.round(a.min)===Math.round(i.min)&&Math.round(a.max)===Math.round(i.max)}function Mg(a,i){return Ey(a.x,i.x)&&Ey(a.y,i.y)}function My(a){return se(a.x)/se(a.y)}function wy(a,i){return a.translate===i.translate&&a.scale===i.scale&&a.originPoint===i.originPoint}class CA{constructor(){this.members=[]}add(i){kc(this.members,i),i.scheduleRender()}remove(i){if(qc(this.members,i),i===this.prevLead&&(this.prevLead=void 0),i===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(i){const s=this.members.findIndex(c=>i===c);if(s===0)return!1;let r;for(let c=s;c>=0;c--){const f=this.members[c];if(f.isPresent!==!1){r=f;break}}return r?(this.promote(r),!0):!1}promote(i,s){const r=this.lead;if(i!==r&&(this.prevLead=r,this.lead=i,i.show(),r)){r.instance&&r.scheduleRender(),i.scheduleRender(),i.resumeFrom=r,s&&(i.resumeFrom.preserveOpacity=!0),r.snapshot&&(i.snapshot=r.snapshot,i.snapshot.latestValues=r.animationValues||r.latestValues),i.root&&i.root.isUpdating&&(i.isLayoutDirty=!0);const{crossfade:c}=i.options;c===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(i=>{const{options:s,resumingFrom:r}=i;s.onExitComplete&&s.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(i=>{i.instance&&i.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function NA(a,i,s){let r="";const c=a.x.translate/i.x,f=a.y.translate/i.y,d=(s==null?void 0:s.z)||0;if((c||f||d)&&(r=`translate3d(${c}px, ${f}px, ${d}px) `),(i.x!==1||i.y!==1)&&(r+=`scale(${1/i.x}, ${1/i.y}) `),s){const{transformPerspective:h,rotate:g,rotateX:x,rotateY:b,skewX:M,skewY:D}=s;h&&(r=`perspective(${h}px) ${r}`),g&&(r+=`rotate(${g}deg) `),x&&(r+=`rotateX(${x}deg) `),b&&(r+=`rotateY(${b}deg) `),M&&(r+=`skewX(${M}deg) `),D&&(r+=`skewY(${D}deg) `)}const p=a.x.scale*i.x,m=a.y.scale*i.y;return(p!==1||m!==1)&&(r+=`scale(${p}, ${m})`),r||"none"}const ic=["","X","Y","Z"],jA={visibility:"hidden"},OA=1e3;let VA=0;function lc(a,i,s,r){const{latestValues:c}=i;c[a]&&(s[a]=c[a],i.setStaticValue(a,0),r&&(r[a]=0))}function wg(a){if(a.hasCheckedOptimisedAppear=!0,a.root===a)return;const{visualElement:i}=a.options;if(!i)return;const s=og(i);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:c,layoutId:f}=a.options;window.MotionCancelOptimisedAnimation(s,"transform",Ot,!(c||f))}const{parent:r}=a;r&&!r.hasCheckedOptimisedAppear&&wg(r)}function Dg({attachResizeListener:a,defaultParent:i,measureScroll:s,checkIsScrollRoot:r,resetTransform:c}){return class{constructor(d={},p=i==null?void 0:i()){this.id=VA++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(UA),this.nodes.forEach(qA),this.nodes.forEach(YA),this.nodes.forEach(BA)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=d,this.root=p?p.root||p:this,this.path=p?[...p.path,p]:[],this.parent=p,this.depth=p?p.depth+1:0;for(let m=0;m<this.path.length;m++)this.path[m].shouldResetTransform=!0;this.root===this&&(this.nodes=new xA)}addEventListener(d,p){return this.eventHandlers.has(d)||this.eventHandlers.set(d,new Xc),this.eventHandlers.get(d).add(p)}notifyListeners(d,...p){const m=this.eventHandlers.get(d);m&&m.notify(...p)}hasListeners(d){return this.eventHandlers.has(d)}mount(d){if(this.instance)return;this.isSVG=Q0(d)&&!V2(d),this.instance=d;const{layoutId:p,layout:m,visualElement:h}=this.options;if(h&&!h.current&&h.mount(d),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(m||p)&&(this.isLayoutDirty=!0),a){let g;const x=()=>this.root.updateBlockedByResize=!1;a(d,()=>{this.root.updateBlockedByResize=!0,g&&g(),g=bA(x,250),fr.hasAnimatedSinceResize&&(fr.hasAnimatedSinceResize=!1,this.nodes.forEach(Ry))})}p&&this.root.registerSharedNode(p,this),this.options.animate!==!1&&h&&(p||m)&&this.addEventListener("didUpdate",({delta:g,hasLayoutChanged:x,hasRelativeLayoutChanged:b,layout:M})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const D=this.options.transition||h.getDefaultTransition()||QA,{onLayoutAnimationStart:z,onLayoutAnimationComplete:B}=h.getProps(),V=!this.targetLayout||!Mg(this.targetLayout,M),q=!x&&b;if(this.options.layoutRoot||this.resumeFrom||q||x&&(V||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const k={...af(D,"layout"),onPlay:z,onComplete:B};(h.shouldReduceMotion||this.options.layoutRoot)&&(k.delay=0,k.type=!1),this.startAnimation(k),this.setAnimationOrigin(g,q)}else x||Ry(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=M})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const d=this.getStack();d&&d.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Kn(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(GA),this.animationId++)}getTransformTemplate(){const{visualElement:d}=this.options;return d&&d.getProps().transformTemplate}willUpdate(d=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&wg(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let g=0;g<this.path.length;g++){const x=this.path[g];x.shouldResetTransform=!0,x.updateScroll("snapshot"),x.options.layoutRoot&&x.willUpdate(!1)}const{layoutId:p,layout:m}=this.options;if(p===void 0&&!m)return;const h=this.getTransformTemplate();this.prevTransformTemplateValue=h?h(this.latestValues,""):void 0,this.updateSnapshot(),d&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Dy);return}this.isUpdating||this.nodes.forEach(HA),this.isUpdating=!1,this.nodes.forEach(kA),this.nodes.forEach(zA),this.nodes.forEach(_A),this.clearAllSnapshots();const p=de.now();te.delta=yn(0,1e3/60,p-te.timestamp),te.timestamp=p,te.isProcessing=!0,$u.update.process(te),$u.preRender.process(te),$u.render.process(te),te.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,sf.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(LA),this.sharedNodes.forEach(XA)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Ot.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Ot.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!se(this.snapshot.measuredBox.x)&&!se(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let m=0;m<this.path.length;m++)this.path[m].updateScroll();const d=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Lt(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:p}=this.options;p&&p.notify("LayoutMeasure",this.layout.layoutBox,d?d.layoutBox:void 0)}updateScroll(d="measure"){let p=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===d&&(p=!1),p&&this.instance){const m=r(this.instance);this.scroll={animationId:this.root.animationId,phase:d,isRoot:m,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:m}}}resetTransform(){if(!c)return;const d=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,p=this.projectionDelta&&!Eg(this.projectionDelta),m=this.getTransformTemplate(),h=m?m(this.latestValues,""):void 0,g=h!==this.prevTransformTemplateValue;d&&this.instance&&(p||ma(this.latestValues)||g)&&(c(this.instance,h),this.shouldResetTransform=!1,this.scheduleRender())}measure(d=!0){const p=this.measurePageBox();let m=this.removeElementScroll(p);return d&&(m=this.removeTransform(m)),PA(m),{animationId:this.root.animationId,measuredBox:p,layoutBox:m,latestValues:{},source:this.id}}measurePageBox(){var h;const{visualElement:d}=this.options;if(!d)return Lt();const p=d.measureViewportBox();if(!(((h=this.scroll)==null?void 0:h.wasRoot)||this.path.some($A))){const{scroll:g}=this.root;g&&(ri(p.x,g.offset.x),ri(p.y,g.offset.y))}return p}removeElementScroll(d){var m;const p=Lt();if(Oe(p,d),(m=this.scroll)!=null&&m.wasRoot)return p;for(let h=0;h<this.path.length;h++){const g=this.path[h],{scroll:x,options:b}=g;g!==this.root&&x&&b.layoutScroll&&(x.wasRoot&&Oe(p,d),ri(p.x,x.offset.x),ri(p.y,x.offset.y))}return p}applyTransform(d,p=!1){const m=Lt();Oe(m,d);for(let h=0;h<this.path.length;h++){const g=this.path[h];!p&&g.options.layoutScroll&&g.scroll&&g!==g.root&&oi(m,{x:-g.scroll.offset.x,y:-g.scroll.offset.y}),ma(g.latestValues)&&oi(m,g.latestValues)}return ma(this.latestValues)&&oi(m,this.latestValues),m}removeTransform(d){const p=Lt();Oe(p,d);for(let m=0;m<this.path.length;m++){const h=this.path[m];if(!h.instance||!ma(h.latestValues))continue;Mc(h.latestValues)&&h.updateSnapshot();const g=Lt(),x=h.measurePageBox();Oe(g,x),Sy(p,h.latestValues,h.snapshot?h.snapshot.layoutBox:void 0,g)}return ma(this.latestValues)&&Sy(p,this.latestValues),p}setTargetDelta(d){this.targetDelta=d,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(d){this.options={...this.options,...d,crossfade:d.crossfade!==void 0?d.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==te.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(d=!1){var b;const p=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=p.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=p.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=p.isSharedProjectionDirty);const m=!!this.resumingFrom||this!==p;if(!(d||m&&this.isSharedProjectionDirty||this.isProjectionDirty||(b=this.parent)!=null&&b.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:g,layoutId:x}=this.options;if(!(!this.layout||!(g||x))){if(this.resolvedRelativeTargetAt=te.timestamp,!this.targetDelta&&!this.relativeTarget){const M=this.getClosestProjectingParent();M&&M.layout&&this.animationProgress!==1?(this.relativeParent=M,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Lt(),this.relativeTargetOrigin=Lt(),El(this.relativeTargetOrigin,this.layout.layoutBox,M.layout.layoutBox),Oe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=Lt(),this.targetWithTransforms=Lt()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),FT(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Oe(this.target,this.layout.layoutBox),yg(this.target,this.targetDelta)):Oe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const M=this.getClosestProjectingParent();M&&!!M.resumingFrom==!!this.resumingFrom&&!M.options.layoutScroll&&M.target&&this.animationProgress!==1?(this.relativeParent=M,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Lt(),this.relativeTargetOrigin=Lt(),El(this.relativeTargetOrigin,this.target,M.target),Oe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Mc(this.parent.latestValues)||pg(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var D;const d=this.getLead(),p=!!this.resumingFrom||this!==d;let m=!0;if((this.isProjectionDirty||(D=this.parent)!=null&&D.isProjectionDirty)&&(m=!1),p&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(m=!1),this.resolvedRelativeTargetAt===te.timestamp&&(m=!1),m)return;const{layout:h,layoutId:g}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(h||g))return;Oe(this.layoutCorrected,this.layout.layoutBox);const x=this.treeScale.x,b=this.treeScale.y;WT(this.layoutCorrected,this.treeScale,this.path,p),d.layout&&!d.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(d.target=d.layout.layoutBox,d.targetWithTransforms=Lt());const{target:M}=d;if(!M){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(vy(this.prevProjectionDelta.x,this.projectionDelta.x),vy(this.prevProjectionDelta.y,this.projectionDelta.y)),Al(this.projectionDelta,this.layoutCorrected,M,this.latestValues),(this.treeScale.x!==x||this.treeScale.y!==b||!wy(this.projectionDelta.x,this.prevProjectionDelta.x)||!wy(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",M))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(d=!0){var p;if((p=this.options.visualElement)==null||p.scheduleRender(),d){const m=this.getStack();m&&m.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=si(),this.projectionDelta=si(),this.projectionDeltaWithTransform=si()}setAnimationOrigin(d,p=!1){const m=this.snapshot,h=m?m.latestValues:{},g={...this.latestValues},x=si();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!p;const b=Lt(),M=m?m.source:void 0,D=this.layout?this.layout.source:void 0,z=M!==D,B=this.getStack(),V=!B||B.members.length<=1,q=!!(z&&!V&&this.options.crossfade===!0&&!this.path.some(ZA));this.animationProgress=0;let k;this.mixTargetDelta=P=>{const G=P/1e3;Cy(x.x,d.x,G),Cy(x.y,d.y,G),this.setTargetDelta(x),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(El(b,this.layout.layoutBox,this.relativeParent.layout.layoutBox),KA(this.relativeTarget,this.relativeTargetOrigin,b,G),k&&RA(this.relativeTarget,k)&&(this.isProjectionDirty=!1),k||(k=Lt()),Oe(k,this.relativeTarget)),z&&(this.animationValues=g,TA(g,h,this.latestValues,G,q,V)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=G},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(d){var p,m,h;this.notifyListeners("animationStart"),(p=this.currentAnimation)==null||p.stop(),(h=(m=this.resumingFrom)==null?void 0:m.currentAnimation)==null||h.stop(),this.pendingAnimation&&(Kn(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Ot.update(()=>{fr.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=ui(0)),this.currentAnimation=gA(this.motionValue,[0,1e3],{...d,velocity:0,isSync:!0,onUpdate:g=>{this.mixTargetDelta(g),d.onUpdate&&d.onUpdate(g)},onStop:()=>{},onComplete:()=>{d.onComplete&&d.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const d=this.getStack();d&&d.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(OA),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const d=this.getLead();let{targetWithTransforms:p,target:m,layout:h,latestValues:g}=d;if(!(!p||!m||!h)){if(this!==d&&this.layout&&h&&Rg(this.options.animationType,this.layout.layoutBox,h.layoutBox)){m=this.target||Lt();const x=se(this.layout.layoutBox.x);m.x.min=d.target.x.min,m.x.max=m.x.min+x;const b=se(this.layout.layoutBox.y);m.y.min=d.target.y.min,m.y.max=m.y.min+b}Oe(p,m),oi(p,g),Al(this.projectionDeltaWithTransform,this.layoutCorrected,p,g)}}registerSharedNode(d,p){this.sharedNodes.has(d)||this.sharedNodes.set(d,new CA),this.sharedNodes.get(d).add(p);const h=p.options.initialPromotionConfig;p.promote({transition:h?h.transition:void 0,preserveFollowOpacity:h&&h.shouldPreserveFollowOpacity?h.shouldPreserveFollowOpacity(p):void 0})}isLead(){const d=this.getStack();return d?d.lead===this:!0}getLead(){var p;const{layoutId:d}=this.options;return d?((p=this.getStack())==null?void 0:p.lead)||this:this}getPrevLead(){var p;const{layoutId:d}=this.options;return d?(p=this.getStack())==null?void 0:p.prevLead:void 0}getStack(){const{layoutId:d}=this.options;if(d)return this.root.sharedNodes.get(d)}promote({needsReset:d,transition:p,preserveFollowOpacity:m}={}){const h=this.getStack();h&&h.promote(this,m),d&&(this.projectionDelta=void 0,this.needsReset=!0),p&&this.setOptions({transition:p})}relegate(){const d=this.getStack();return d?d.relegate(this):!1}resetSkewAndRotation(){const{visualElement:d}=this.options;if(!d)return;let p=!1;const{latestValues:m}=d;if((m.z||m.rotate||m.rotateX||m.rotateY||m.rotateZ||m.skewX||m.skewY)&&(p=!0),!p)return;const h={};m.z&&lc("z",d,h,this.animationValues);for(let g=0;g<ic.length;g++)lc(`rotate${ic[g]}`,d,h,this.animationValues),lc(`skew${ic[g]}`,d,h,this.animationValues);d.render();for(const g in h)d.setStaticValue(g,h[g]),this.animationValues&&(this.animationValues[g]=h[g]);d.scheduleRender()}getProjectionStyles(d){if(!this.instance||this.isSVG)return;if(!this.isVisible)return jA;const p={visibility:""},m=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,p.opacity="",p.pointerEvents=cr(d==null?void 0:d.pointerEvents)||"",p.transform=m?m(this.latestValues,""):"none",p;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const M={};return this.options.layoutId&&(M.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,M.pointerEvents=cr(d==null?void 0:d.pointerEvents)||""),this.hasProjected&&!ma(this.latestValues)&&(M.transform=m?m({},""):"none",this.hasProjected=!1),M}const g=h.animationValues||h.latestValues;this.applyTransformsToTarget(),p.transform=NA(this.projectionDeltaWithTransform,this.treeScale,g),m&&(p.transform=m(g,p.transform));const{x,y:b}=this.projectionDelta;p.transformOrigin=`${x.origin*100}% ${b.origin*100}% 0`,h.animationValues?p.opacity=h===this?g.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:g.opacityExit:p.opacity=h===this?g.opacity!==void 0?g.opacity:"":g.opacityExit!==void 0?g.opacityExit:0;for(const M in jl){if(g[M]===void 0)continue;const{correct:D,applyTo:z,isCSSVariable:B}=jl[M],V=p.transform==="none"?g[M]:D(g[M],h);if(z){const q=z.length;for(let k=0;k<q;k++)p[z[k]]=V}else B?this.options.visualElement.renderState.vars[M]=V:p[M]=V}return this.options.layoutId&&(p.pointerEvents=h===this?cr(d==null?void 0:d.pointerEvents)||"":"none"),p}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(d=>{var p;return(p=d.currentAnimation)==null?void 0:p.stop()}),this.root.nodes.forEach(Dy),this.root.sharedNodes.clear()}}}function zA(a){a.updateLayout()}function _A(a){var s;const i=((s=a.resumeFrom)==null?void 0:s.snapshot)||a.snapshot;if(a.isLead()&&a.layout&&i&&a.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:c}=a.layout,{animationType:f}=a.options,d=i.source!==a.layout.source;f==="size"?Ve(x=>{const b=d?i.measuredBox[x]:i.layoutBox[x],M=se(b);b.min=r[x].min,b.max=b.min+M}):Rg(f,i.layoutBox,r)&&Ve(x=>{const b=d?i.measuredBox[x]:i.layoutBox[x],M=se(r[x]);b.max=b.min+M,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[x].max=a.relativeTarget[x].min+M)});const p=si();Al(p,r,i.layoutBox);const m=si();d?Al(m,a.applyTransform(c,!0),i.measuredBox):Al(m,r,i.layoutBox);const h=!Eg(p);let g=!1;if(!a.resumeFrom){const x=a.getClosestProjectingParent();if(x&&!x.resumeFrom){const{snapshot:b,layout:M}=x;if(b&&M){const D=Lt();El(D,i.layoutBox,b.layoutBox);const z=Lt();El(z,r,M.layoutBox),Mg(D,z)||(g=!0),x.options.layoutRoot&&(a.relativeTarget=z,a.relativeTargetOrigin=D,a.relativeParent=x)}}}a.notifyListeners("didUpdate",{layout:r,snapshot:i,delta:m,layoutDelta:p,hasLayoutChanged:h,hasRelativeLayoutChanged:g})}else if(a.isLead()){const{onExitComplete:r}=a.options;r&&r()}a.options.transition=void 0}function UA(a){a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function BA(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function LA(a){a.clearSnapshot()}function Dy(a){a.clearMeasurements()}function HA(a){a.isLayoutDirty=!1}function kA(a){const{visualElement:i}=a.options;i&&i.getProps().onBeforeLayoutMeasure&&i.notify("BeforeLayoutMeasure"),a.resetTransform()}function Ry(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function qA(a){a.resolveTargetDelta()}function YA(a){a.calcProjection()}function GA(a){a.resetSkewAndRotation()}function XA(a){a.removeLeadSnapshot()}function Cy(a,i,s){a.translate=Nt(i.translate,0,s),a.scale=Nt(i.scale,1,s),a.origin=i.origin,a.originPoint=i.originPoint}function Ny(a,i,s,r){a.min=Nt(i.min,s.min,r),a.max=Nt(i.max,s.max,r)}function KA(a,i,s,r){Ny(a.x,i.x,s.x,r),Ny(a.y,i.y,s.y,r)}function ZA(a){return a.animationValues&&a.animationValues.opacityExit!==void 0}const QA={duration:.45,ease:[.4,0,.1,1]},jy=a=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),Oy=jy("applewebkit/")&&!jy("chrome/")?Math.round:ze;function Vy(a){a.min=Oy(a.min),a.max=Oy(a.max)}function PA(a){Vy(a.x),Vy(a.y)}function Rg(a,i,s){return a==="position"||a==="preserve-aspect"&&!JT(My(i),My(s),.2)}function $A(a){var i;return a!==a.root&&((i=a.scroll)==null?void 0:i.wasRoot)}const JA=Dg({attachResizeListener:(a,i)=>Vl(a,"resize",i),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),sc={current:void 0},Cg=Dg({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!sc.current){const a=new JA({});a.mount(window),a.setOptions({layoutScroll:!0}),sc.current=a}return sc.current},resetTransform:(a,i)=>{a.style.transform=i!==void 0?i:"none"},checkIsScrollRoot:a=>window.getComputedStyle(a).position==="fixed"}),FA={pan:{Feature:hA},drag:{Feature:dA,ProjectionNode:Cg,MeasureLayout:Sg}};function zy(a,i,s){const{props:r}=a;a.animationState&&r.whileHover&&a.animationState.setActive("whileHover",s==="Start");const c="onHover"+s,f=r[c];f&&Ot.postRender(()=>f(i,kl(i)))}class WA extends Pn{mount(){const{current:i}=this.node;i&&(this.unmount=R2(i,(s,r)=>(zy(this.node,r,"Start"),c=>zy(this.node,c,"End"))))}unmount(){}}class IA extends Pn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let i=!1;try{i=this.node.current.matches(":focus-visible")}catch{i=!0}!i||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Bl(Vl(this.node.current,"focus",()=>this.onFocus()),Vl(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function _y(a,i,s){const{props:r}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&r.whileTap&&a.animationState.setActive("whileTap",s==="Start");const c="onTap"+(s==="End"?"":s),f=r[c];f&&Ot.postRender(()=>f(i,kl(i)))}class tE extends Pn{mount(){const{current:i}=this.node;i&&(this.unmount=O2(i,(s,r)=>(_y(this.node,r,"Start"),(c,{success:f})=>_y(this.node,c,f?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Rc=new WeakMap,rc=new WeakMap,eE=a=>{const i=Rc.get(a.target);i&&i(a)},nE=a=>{a.forEach(eE)};function aE({root:a,...i}){const s=a||document;rc.has(s)||rc.set(s,{});const r=rc.get(s),c=JSON.stringify(i);return r[c]||(r[c]=new IntersectionObserver(nE,{root:a,...i})),r[c]}function iE(a,i,s){const r=aE(i);return Rc.set(a,s),r.observe(a),()=>{Rc.delete(a),r.unobserve(a)}}const lE={some:0,all:1};class sE extends Pn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:i={}}=this.node.getProps(),{root:s,margin:r,amount:c="some",once:f}=i,d={root:s?s.current:void 0,rootMargin:r,threshold:typeof c=="number"?c:lE[c]},p=m=>{const{isIntersecting:h}=m;if(this.isInView===h||(this.isInView=h,f&&!h&&this.hasEnteredView))return;h&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",h);const{onViewportEnter:g,onViewportLeave:x}=this.node.getProps(),b=h?g:x;b&&b(m)};return iE(this.node.current,d,p)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:i,prevProps:s}=this.node;["amount","margin","root"].some(rE(i,s))&&this.startObserver()}unmount(){}}function rE({viewport:a={}},{viewport:i={}}={}){return s=>a[s]!==i[s]}const oE={inView:{Feature:sE},tap:{Feature:tE},focus:{Feature:IA},hover:{Feature:WA}},uE={layout:{ProjectionNode:Cg,MeasureLayout:Sg}},Cc={current:null},Ng={current:!1};function cE(){if(Ng.current=!0,!!Lc)if(window.matchMedia){const a=window.matchMedia("(prefers-reduced-motion)"),i=()=>Cc.current=a.matches;a.addListener(i),i()}else Cc.current=!1}const fE=new WeakMap;function dE(a,i,s){for(const r in i){const c=i[r],f=s[r];if(ie(c))a.addValue(r,c);else if(ie(f))a.addValue(r,ui(c,{owner:a}));else if(f!==c)if(a.hasValue(r)){const d=a.getValue(r);d.liveStyle===!0?d.jump(c):d.hasAnimated||d.set(c)}else{const d=a.getStaticValue(r);a.addValue(r,ui(d!==void 0?d:c,{owner:a}))}}for(const r in s)i[r]===void 0&&a.removeValue(r);return i}const Uy=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class hE{scrapeMotionValuesFromProps(i,s,r){return{}}constructor({parent:i,props:s,presenceContext:r,reducedMotionConfig:c,blockInitialAnimation:f,visualState:d},p={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=nf,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const b=de.now();this.renderScheduledAt<b&&(this.renderScheduledAt=b,Ot.render(this.render,!1,!0))};const{latestValues:m,renderState:h}=d;this.latestValues=m,this.baseTarget={...m},this.initialValues=s.initial?{...m}:{},this.renderState=h,this.parent=i,this.props=s,this.presenceContext=r,this.depth=i?i.depth+1:0,this.reducedMotionConfig=c,this.options=p,this.blockInitialAnimation=!!f,this.isControllingVariants=Er(s),this.isVariantNode=F0(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(i&&i.current);const{willChange:g,...x}=this.scrapeMotionValuesFromProps(s,{},this);for(const b in x){const M=x[b];m[b]!==void 0&&ie(M)&&M.set(m[b],!1)}}mount(i){this.current=i,fE.set(i,this),this.projection&&!this.projection.instance&&this.projection.mount(i),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,r)=>this.bindToMotionValue(r,s)),Ng.current||cE(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Cc.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Kn(this.notifyUpdate),Kn(this.render),this.valueSubscriptions.forEach(i=>i()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const i in this.events)this.events[i].clear();for(const i in this.features){const s=this.features[i];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(i,s){this.valueSubscriptions.has(i)&&this.valueSubscriptions.get(i)();const r=pi.has(i);r&&this.onBindTransform&&this.onBindTransform();const c=s.on("change",p=>{this.latestValues[i]=p,this.props.onUpdate&&Ot.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),f=s.on("renderRequest",this.scheduleRender);let d;window.MotionCheckAppearSync&&(d=window.MotionCheckAppearSync(this,i,s)),this.valueSubscriptions.set(i,()=>{c(),f(),d&&d(),s.owner&&s.stop()})}sortNodePosition(i){return!this.current||!this.sortInstanceNodePosition||this.type!==i.type?0:this.sortInstanceNodePosition(this.current,i.current)}updateFeatures(){let i="animation";for(i in ci){const s=ci[i];if(!s)continue;const{isEnabled:r,Feature:c}=s;if(!this.features[i]&&c&&r(this.props)&&(this.features[i]=new c(this)),this.features[i]){const f=this.features[i];f.isMounted?f.update():(f.mount(),f.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Lt()}getStaticValue(i){return this.latestValues[i]}setStaticValue(i,s){this.latestValues[i]=s}update(i,s){(i.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=i,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let r=0;r<Uy.length;r++){const c=Uy[r];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);const f="on"+c,d=i[f];d&&(this.propEventSubscriptions[c]=this.on(c,d))}this.prevMotionValues=dE(this,this.scrapeMotionValuesFromProps(i,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(i){return this.props.variants?this.props.variants[i]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(i){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(i),()=>s.variantChildren.delete(i)}addValue(i,s){const r=this.values.get(i);s!==r&&(r&&this.removeValue(i),this.bindToMotionValue(i,s),this.values.set(i,s),this.latestValues[i]=s.get())}removeValue(i){this.values.delete(i);const s=this.valueSubscriptions.get(i);s&&(s(),this.valueSubscriptions.delete(i)),delete this.latestValues[i],this.removeValueFromRenderState(i,this.renderState)}hasValue(i){return this.values.has(i)}getValue(i,s){if(this.props.values&&this.props.values[i])return this.props.values[i];let r=this.values.get(i);return r===void 0&&s!==void 0&&(r=ui(s===null?void 0:s,{owner:this}),this.addValue(i,r)),r}readValue(i,s){let r=this.latestValues[i]!==void 0||!this.current?this.latestValues[i]:this.getBaseTargetFromProps(this.props,i)??this.readValueFromInstance(this.current,i,this.options);return r!=null&&(typeof r=="string"&&(i0(r)||s0(r))?r=parseFloat(r):!_2(r)&&Zn.test(s)&&(r=Y0(i,s)),this.setBaseTarget(i,ie(r)?r.get():r)),ie(r)?r.get():r}setBaseTarget(i,s){this.baseTarget[i]=s}getBaseTarget(i){var f;const{initial:s}=this.props;let r;if(typeof s=="string"||typeof s=="object"){const d=mf(this.props,s,(f=this.presenceContext)==null?void 0:f.custom);d&&(r=d[i])}if(s&&r!==void 0)return r;const c=this.getBaseTargetFromProps(this.props,i);return c!==void 0&&!ie(c)?c:this.initialValues[i]!==void 0&&r===void 0?void 0:this.baseTarget[i]}on(i,s){return this.events[i]||(this.events[i]=new Xc),this.events[i].add(s)}notify(i,...s){this.events[i]&&this.events[i].notify(...s)}}class jg extends hE{constructor(){super(...arguments),this.KeyframeResolver=A2}sortInstanceNodePosition(i,s){return i.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(i,s){return i.style?i.style[s]:void 0}removeValueFromRenderState(i,{vars:s,style:r}){delete s[i],delete r[i]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:i}=this.props;ie(i)&&(this.childSubscription=i.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function Og(a,{style:i,vars:s},r,c){Object.assign(a.style,i,c&&c.getProjectionStyles(r));for(const f in s)a.style.setProperty(f,s[f])}function mE(a){return window.getComputedStyle(a)}class pE extends jg{constructor(){super(...arguments),this.type="html",this.renderInstance=Og}readValueFromInstance(i,s){var r;if(pi.has(s))return(r=this.projection)!=null&&r.isProjecting?yc(s):YS(i,s);{const c=mE(i),f=(Qc(s)?c.getPropertyValue(s):c[s])||0;return typeof f=="string"?f.trim():f}}measureInstanceViewportBox(i,{transformPagePoint:s}){return gg(i,s)}build(i,s,r){ff(i,s,r.transformTemplate)}scrapeMotionValuesFromProps(i,s,r){return pf(i,s,r)}}const Vg=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function yE(a,i,s,r){Og(a,i,void 0,r);for(const c in i.attrs)a.setAttribute(Vg.has(c)?c:cf(c),i.attrs[c])}class gE extends jg{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Lt}getBaseTargetFromProps(i,s){return i[s]}readValueFromInstance(i,s){if(pi.has(s)){const r=q0(s);return r&&r.default||0}return s=Vg.has(s)?s:cf(s),i.getAttribute(s)}scrapeMotionValuesFromProps(i,s,r){return rg(i,s,r)}build(i,s,r){ag(i,s,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(i,s,r,c){yE(i,s,r,c)}mount(i){this.isSVGTag=lg(i.tagName),super.mount(i)}}const vE=(a,i)=>hf(a)?new gE(i):new pE(i,{allowProjection:a!==C.Fragment}),xE=yT({...YT,...oE,...FA,...uE},vE),dt=q2(xE),bE=({children:a})=>{const[i,s]=C.useState(""),r=Qn(),c=[{name:"About Me",path:"/",id:"about"},{name:"Projects",path:"/projects",id:"projects"},{name:"Services",path:"/services",id:"services"}];return C.useEffect(()=>{const f=r.pathname;f==="/"?s("about"):f==="/projects"?s("projects"):f==="/services"?s("services"):f==="/contact"&&s("contact")},[r]),S.jsxs("div",{className:"min-h-screen bg-white",children:[S.jsxs(dt.nav,{className:"fixed top-0 left-0 z-50 h-screen w-16 md:w-20 lg:w-24 bg-white border-r border-gray-200 flex flex-col justify-between py-6 md:py-8",initial:{x:-100,opacity:0},animate:{x:0,opacity:1},transition:{duration:.6,ease:"easeOut"},children:[S.jsx("div",{className:"flex flex-col space-y-8",children:c.map((f,d)=>S.jsx(dt.div,{initial:{x:-50,opacity:0},animate:{x:0,opacity:1},transition:{duration:.6,delay:d*.1},children:S.jsx(hr,{to:f.path,className:`block transform -rotate-90 origin-center whitespace-nowrap text-xs md:text-sm font-medium transition-all duration-300 hover:text-gray-600 ${i===f.id?"text-black font-semibold":"text-gray-500"}`,style:{transformOrigin:"center center"},children:f.name})},f.id))}),S.jsx(dt.div,{initial:{y:50,opacity:0},animate:{y:0,opacity:1},transition:{duration:.6,delay:.4},children:S.jsx(hr,{to:"/contact",className:"block transform -rotate-90 origin-center bg-black text-white px-3 md:px-4 py-2 text-xs md:text-sm font-medium hover:bg-gray-800 transition-all duration-300 whitespace-nowrap",style:{transformOrigin:"center center"},children:"Contact Now"})})]}),S.jsx("main",{className:"ml-16 md:ml-20 lg:ml-24",children:a})]})},SE=()=>{const[a,i]=C.useState([]);C.useEffect(()=>{i((()=>{const f=[],d=new Date,p=new Date(d.getFullYear(),d.getMonth()-11,1);for(let m=0;m<365;m++){const h=new Date(p);h.setDate(p.getDate()+m);const g=Math.floor(Math.random()*5);f.push({date:h.toISOString().split("T")[0],intensity:g,commits:g*Math.floor(Math.random()*3)+g})}return f})())},[]);const s=c=>{const f=["bg-gray-800","bg-gray-600","bg-gray-500","bg-gray-400","bg-white"];return f[c]||f[0]},r=[];for(let c=0;c<a.length;c+=7)r.push(a.slice(c,c+7));return S.jsx("div",{className:"bg-black p-6 rounded-lg border border-gray-800",children:S.jsxs(dt.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"space-y-4",children:[S.jsx("h3",{className:"text-white text-lg font-medium mb-4",children:"GitHub Activity"}),S.jsx("div",{className:"flex gap-1 max-w-full overflow-x-auto",children:r.map((c,f)=>S.jsx("div",{className:"flex flex-col gap-1",children:c.map((d,p)=>S.jsx(dt.div,{initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{duration:.2,delay:(f*7+p)*.001},className:`w-3 h-3 rounded-sm ${s((d==null?void 0:d.intensity)||0)} hover:ring-2 hover:ring-white hover:ring-opacity-50 transition-all duration-200 cursor-pointer`,title:d?`${d.commits} commits on ${d.date}`:""},`${f}-${p}`))},f))}),S.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-400 mt-4",children:[S.jsx("span",{children:"Less"}),S.jsx("div",{className:"flex gap-1",children:[0,1,2,3,4].map(c=>S.jsx("div",{className:`w-3 h-3 rounded-sm ${s(c)}`},c))}),S.jsx("span",{children:"More"})]}),S.jsxs("div",{className:"grid grid-cols-2 gap-4 mt-6 text-center",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-2xl font-bold text-white",children:a.reduce((c,f)=>c+f.commits,0)}),S.jsx("div",{className:"text-sm text-gray-400",children:"Total Commits"})]}),S.jsxs("div",{children:[S.jsx("div",{className:"text-2xl font-bold text-white",children:a.filter(c=>c.commits>0).length}),S.jsx("div",{className:"text-sm text-gray-400",children:"Active Days"})]})]})]})})},TE=()=>{const[a,i]=C.useState(""),s="Turning ideas into reality with code & creativity";return C.useEffect(()=>{let r=0;const c=setInterval(()=>{r<s.length?(i(s.slice(0,r+1)),r++):clearInterval(c)},50);return()=>clearInterval(c)},[]),S.jsxs("section",{className:"min-h-screen bg-black text-white flex items-center justify-center relative overflow-hidden",children:[S.jsx("div",{className:"absolute inset-0 opacity-5",children:S.jsx("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle at 1px 1px, white 1px, transparent 0)",backgroundSize:"50px 50px"}})}),S.jsx("div",{className:"container-max section-padding relative z-10",children:S.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[S.jsxs(dt.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.8,ease:"easeOut"},className:"space-y-8",children:[S.jsxs("div",{className:"space-y-4",children:[S.jsx(dt.h1,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"text-5xl md:text-7xl font-bold leading-tight",children:"Arkit Karmokar"}),S.jsxs(dt.p,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-2xl md:text-3xl text-gray-300 font-light",children:["aka ",S.jsx("span",{className:"text-white font-medium",children:"Maddy"})]})]}),S.jsx(dt.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:.6},className:"h-16 flex items-center",children:S.jsxs("p",{className:"text-xl md:text-2xl text-gray-400 font-light",children:[a,S.jsx("span",{className:"animate-pulse",children:"|"})]})}),S.jsxs(dt.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"flex flex-col sm:flex-row gap-4",children:[S.jsx("button",{className:"bg-white text-black px-8 py-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300 transform hover:scale-105",children:"View My Work"}),S.jsx("button",{className:"border border-white text-white px-8 py-4 rounded-lg font-medium hover:bg-white hover:text-black transition-all duration-300",children:"Get In Touch"})]})]}),S.jsx(dt.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.4},className:"flex justify-center lg:justify-end",children:S.jsx(SE,{})})]})}),S.jsx(dt.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:1.2},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:S.jsx("div",{className:"w-6 h-10 border-2 border-white rounded-full flex justify-center",children:S.jsx(dt.div,{animate:{y:[0,12,0]},transition:{duration:1.5,repeat:1/0},className:"w-1 h-3 bg-white rounded-full mt-2"})})})]})};/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AE=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),EE=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(i,s,r)=>r?r.toUpperCase():s.toLowerCase()),By=a=>{const i=EE(a);return i.charAt(0).toUpperCase()+i.slice(1)},zg=(...a)=>a.filter((i,s,r)=>!!i&&i.trim()!==""&&r.indexOf(i)===s).join(" ").trim(),ME=a=>{for(const i in a)if(i.startsWith("aria-")||i==="role"||i==="title")return!0};/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var wE={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const DE=C.forwardRef(({color:a="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:r,className:c="",children:f,iconNode:d,...p},m)=>C.createElement("svg",{ref:m,...wE,width:i,height:i,stroke:a,strokeWidth:r?Number(s)*24/Number(i):s,className:zg("lucide",c),...!f&&!ME(p)&&{"aria-hidden":"true"},...p},[...d.map(([h,g])=>C.createElement(h,g)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wt=(a,i)=>{const s=C.forwardRef(({className:r,...c},f)=>C.createElement(DE,{ref:f,iconNode:i,className:zg(`lucide-${AE(By(a))}`,`lucide-${a}`,r),...c}));return s.displayName=By(a),s};/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const RE=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],CE=wt("arrow-right",RE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const NE=[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]],xr=wt("brain",NE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jE=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],OE=wt("check",jE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const VE=[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]],zE=wt("cloud",VE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _E=[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]],Nc=wt("code",_E);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const UE=[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]],Ly=wt("database",UE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const BE=[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]],LE=wt("external-link",BE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const HE=[["line",{x1:"6",x2:"6",y1:"3",y2:"15",key:"17qcm7"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M18 9a9 9 0 0 1-9 9",key:"n2h4wq"}]],kE=wt("git-branch",HE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qE=[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]],jc=wt("github",qE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const YE=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],Ml=wt("globe",YE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const GE=[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]],XE=wt("heart",GE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const KE=[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]],ZE=wt("layers",KE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const QE=[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]],PE=wt("lightbulb",QE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $E=[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]],JE=wt("linkedin",$E);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const FE=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],WE=wt("mail",FE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IE=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],t3=wt("map-pin",IE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e3=[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]],Hy=wt("palette",e3);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n3=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],a3=wt("phone",n3);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i3=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],l3=wt("send",i3);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const s3=[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]],ky=wt("server",s3);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r3=[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]],gf=wt("smartphone",r3);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const o3=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]],u3=wt("target",o3);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c3=[["path",{d:"M12 19h8",key:"baeox8"}],["path",{d:"m4 17 6-6-6-6",key:"1yngyt"}]],f3=wt("terminal",c3);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d3=[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]],h3=wt("twitter",d3);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m3=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],p3=wt("zap",m3),y3=()=>{const a=[{name:"HTML",icon:Nc,color:"hover:text-orange-500"},{name:"CSS",icon:Hy,color:"hover:text-blue-500"},{name:"JavaScript",icon:p3,color:"hover:text-yellow-500"},{name:"React",icon:Ml,color:"hover:text-cyan-500"},{name:"Node.js",icon:ky,color:"hover:text-green-500"},{name:"TypeScript",icon:Nc,color:"hover:text-blue-600"},{name:"Tailwind",icon:Hy,color:"hover:text-teal-500"},{name:"Express",icon:ky,color:"hover:text-gray-600"},{name:"PostgreSQL",icon:Ly,color:"hover:text-blue-700"},{name:"MongoDB",icon:Ly,color:"hover:text-green-600"},{name:"Python",icon:f3,color:"hover:text-yellow-600"},{name:"AI/ML",icon:xr,color:"hover:text-purple-500"},{name:"Git",icon:kE,color:"hover:text-red-500"},{name:"Docker",icon:ZE,color:"hover:text-blue-400"},{name:"AWS",icon:zE,color:"hover:text-orange-400"},{name:"Android",icon:gf,color:"hover:text-green-400"}],i=[...a,...a];return S.jsx("section",{className:"py-20 bg-black overflow-hidden",children:S.jsxs("div",{className:"container-max section-padding",children:[S.jsxs(dt.div,{initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12",children:[S.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:"Tech Stack"}),S.jsx("p",{className:"text-xl text-gray-400 max-w-2xl mx-auto",children:"Technologies I use to bring ideas to life"})]}),S.jsxs("div",{className:"relative",children:[S.jsx(dt.div,{className:"flex space-x-8",animate:{x:[0,-50*a.length]},transition:{x:{repeat:1/0,repeatType:"loop",duration:30,ease:"linear"}},children:i.map((s,r)=>{const c=s.icon;return S.jsx(dt.div,{className:"flex-shrink-0 group",whileHover:{scale:1.1},transition:{duration:.3},children:S.jsxs("div",{className:"w-24 h-24 bg-white rounded-xl flex flex-col items-center justify-center space-y-2 group-hover:bg-gray-100 transition-all duration-300 shadow-lg group-hover:shadow-xl",children:[S.jsx(c,{size:32,className:`text-black transition-colors duration-300 ${s.color}`}),S.jsx("span",{className:"text-xs font-medium text-black",children:s.name})]})},`${s.name}-${r}`)})}),S.jsx("div",{className:"absolute top-0 left-0 w-32 h-full bg-gradient-to-r from-black to-transparent z-10"}),S.jsx("div",{className:"absolute top-0 right-0 w-32 h-full bg-gradient-to-l from-black to-transparent z-10"})]}),S.jsx(dt.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.3},viewport:{once:!0},className:"text-center mt-16",children:S.jsxs("p",{className:"text-gray-400 text-lg",children:["Always learning, always growing. Currently exploring"," ",S.jsx("span",{className:"text-white font-medium",children:"Next.js"})," and"," ",S.jsx("span",{className:"text-white font-medium",children:"Machine Learning"})]})})]})})},g3=()=>{const a=[{icon:Nc,title:"Full-Stack Developer",description:"Building end-to-end solutions with modern technologies"},{icon:XE,title:"Passion-Driven",description:"Love turning complex problems into elegant solutions"},{icon:PE,title:"Creative Thinker",description:"Always exploring new ways to innovate and improve"},{icon:u3,title:"Goal-Oriented",description:"Focused on delivering results that make a difference"}];return S.jsx("section",{className:"py-20 bg-white",children:S.jsx("div",{className:"container-max section-padding",children:S.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center",children:[S.jsxs(dt.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[S.jsxs("div",{children:[S.jsx(dt.h2,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"text-4xl md:text-5xl font-bold text-black mb-6",children:"About Me"}),S.jsxs(dt.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"space-y-6 text-lg text-gray-700 leading-relaxed",children:[S.jsxs("p",{children:["Hi, I'm ",S.jsx("span",{className:"font-semibold text-black",children:"Arkit (Maddy)"})," — a full-stack developer passionate about building creative digital experiences and AI agents that think."]}),S.jsx("p",{children:"My journey in tech is driven by curiosity and the belief that great software should not only solve problems but also inspire and delight users. I love the challenge of turning complex ideas into clean, functional code."}),S.jsx("p",{children:"When I'm not coding, you'll find me exploring the latest in AI and machine learning, contributing to open-source projects, or experimenting with new frameworks and technologies."})]})]}),S.jsxs(dt.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.6},viewport:{once:!0},className:"bg-gray-50 p-6 rounded-lg border-l-4 border-black",children:[S.jsx("h3",{className:"text-xl font-semibold text-black mb-3",children:"My Philosophy"}),S.jsx("p",{className:"text-gray-700 italic",children:'"Code is poetry written in logic. Every line should serve a purpose, every function should tell a story, and every project should make the world a little bit better."'})]})]}),S.jsxs(dt.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.3},viewport:{once:!0},className:"space-y-8",children:[S.jsx("div",{className:"flex justify-center lg:justify-start mb-8",children:S.jsx("div",{className:"w-64 h-64 bg-gradient-to-br from-gray-200 to-gray-300 rounded-2xl flex items-center justify-center shadow-lg",children:S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"w-20 h-20 bg-black rounded-full mx-auto mb-4 flex items-center justify-center",children:S.jsx("span",{className:"text-white text-2xl font-bold",children:"AK"})}),S.jsx("p",{className:"text-gray-600 font-medium",children:"Arkit Karmokar"}),S.jsx("p",{className:"text-gray-500 text-sm",children:"aka Maddy"})]})})}),S.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:a.map((i,s)=>{const r=i.icon;return S.jsxs(dt.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*s},viewport:{once:!0},className:"bg-white p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-300 group",children:[S.jsx(r,{size:32,className:"text-black mb-4 group-hover:scale-110 transition-transform duration-300"}),S.jsx("h4",{className:"font-semibold text-black mb-2",children:i.title}),S.jsx("p",{className:"text-gray-600 text-sm",children:i.description})]},s)})})]})]})})})},v3=()=>S.jsxs("div",{children:[S.jsx(TE,{}),S.jsx(y3,{}),S.jsx(g3,{})]}),x3=()=>{const a=[{id:1,title:"AI-Powered Task Manager",description:"A smart task management application that uses machine learning to prioritize tasks and predict completion times. Built with React, Node.js, and TensorFlow.js.",tags:["React","Node.js","AI/ML","MongoDB","TensorFlow.js"],type:"Web App",icon:xr,github:"https://github.com/arkitkarmokar",demo:"https://demo.example.com",featured:!0},{id:2,title:"E-Commerce Platform",description:"Full-stack e-commerce solution with real-time inventory management, payment integration, and admin dashboard. Scalable architecture with microservices.",tags:["React","Express","PostgreSQL","Stripe","Docker"],type:"Web App",icon:Ml,github:"https://github.com/arkitkarmokar",demo:"https://demo.example.com",featured:!0},{id:3,title:"Fitness Tracking App",description:"Android application for tracking workouts, nutrition, and progress. Features offline sync, custom workout plans, and social sharing capabilities.",tags:["Android","Kotlin","Room DB","Firebase","Material Design"],type:"Mobile App",icon:gf,github:"https://github.com/arkitkarmokar",demo:"https://play.google.com/store",featured:!1},{id:4,title:"Real-time Chat Application",description:"Scalable chat application with real-time messaging, file sharing, and video calls. Built with modern web technologies and WebRTC.",tags:["React","Socket.io","WebRTC","Node.js","Redis"],type:"Web App",icon:Ml,github:"https://github.com/arkitkarmokar",demo:"https://demo.example.com",featured:!1},{id:5,title:"Smart Home Dashboard",description:"IoT dashboard for controlling and monitoring smart home devices. Features real-time data visualization and automated scheduling.",tags:["React","Python","IoT","MQTT","InfluxDB"],type:"IoT App",icon:xr,github:"https://github.com/arkitkarmokar",demo:"https://demo.example.com",featured:!1},{id:6,title:"Portfolio Website",description:"Modern, responsive portfolio website built with React and Framer Motion. Features smooth animations and optimized performance.",tags:["React","Tailwind","Framer Motion","Vite"],type:"Web App",icon:Ml,github:"https://github.com/arkitkarmokar",demo:"https://arkitkarmokar.dev",featured:!1}];return S.jsx("section",{className:"py-20",children:S.jsxs("div",{className:"container-max section-padding",children:[S.jsxs(dt.div,{initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[S.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-black mb-6",children:"Featured Projects"}),S.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"A showcase of my recent work, from web applications to mobile apps and AI-powered solutions"})]}),S.jsx("div",{className:"space-y-8",children:a.map((i,s)=>{const r=i.icon,c=s%2===0;return S.jsx(dt.div,{initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:s*.1},viewport:{once:!0},className:`rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 ${c?"bg-black text-white":"bg-white text-black border border-gray-200"}`,children:S.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 min-h-[400px]",children:[S.jsx("div",{className:`p-8 lg:p-12 flex flex-col justify-center ${c?"lg:order-1":"lg:order-2"}`,children:S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{className:"flex items-center space-x-3",children:[S.jsx(r,{size:24,className:c?"text-white":"text-black"}),S.jsx("span",{className:`text-sm font-medium ${c?"text-gray-300":"text-gray-600"}`,children:i.type}),i.featured&&S.jsx("span",{className:"bg-yellow-400 text-black px-2 py-1 rounded-full text-xs font-medium",children:"Featured"})]}),S.jsx("h3",{className:"text-2xl md:text-3xl font-bold",children:i.title}),S.jsx("p",{className:`text-lg leading-relaxed ${c?"text-gray-300":"text-gray-600"}`,children:i.description}),S.jsx("div",{className:"flex flex-wrap gap-2",children:i.tags.map((f,d)=>S.jsx("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${c?"bg-white bg-opacity-20 text-white":"bg-black bg-opacity-10 text-black"}`,children:f},d))}),S.jsxs("div",{className:"flex space-x-4 pt-4",children:[S.jsxs("a",{href:i.github,target:"_blank",rel:"noopener noreferrer",className:`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-300 ${c?"bg-white text-black hover:bg-gray-200":"bg-black text-white hover:bg-gray-800"}`,children:[S.jsx(jc,{size:20}),S.jsx("span",{children:"Code"})]}),S.jsxs("a",{href:i.demo,target:"_blank",rel:"noopener noreferrer",className:`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium border transition-all duration-300 ${c?"border-white text-white hover:bg-white hover:text-black":"border-black text-black hover:bg-black hover:text-white"}`,children:[S.jsx(LE,{size:20}),S.jsx("span",{children:"Demo"})]})]})]})}),S.jsx("div",{className:`${c?"lg:order-2":"lg:order-1"} ${c?"bg-gray-800":"bg-gray-100"} flex items-center justify-center min-h-[300px] lg:min-h-[400px]`,children:S.jsxs("div",{className:"text-center",children:[S.jsx(r,{size:80,className:`mx-auto mb-4 ${c?"text-gray-600":"text-gray-400"}`}),S.jsx("p",{className:`text-lg font-medium ${c?"text-gray-400":"text-gray-500"}`,children:"Project Screenshot"})]})})]})},i.id)})}),S.jsx(dt.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.3},viewport:{once:!0},className:"text-center mt-16",children:S.jsxs("a",{href:"https://github.com/arkitkarmokar",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center space-x-2 bg-black text-white px-8 py-4 rounded-lg font-medium hover:bg-gray-800 transition-all duration-300 transform hover:scale-105",children:[S.jsx(jc,{size:20}),S.jsx("span",{children:"View All Projects"})]})})]})})},b3=()=>S.jsx("div",{children:S.jsx(x3,{})}),S3=()=>{const a=[{id:1,icon:Ml,title:"Website Development",description:"Modern, responsive websites and web applications built with cutting-edge technologies",features:["Responsive Design","Performance Optimization","SEO Friendly","Modern Frameworks","E-commerce Solutions","CMS Integration"],technologies:["React","Next.js","Node.js","TypeScript","Tailwind CSS"],price:"Starting at $2,000",popular:!1},{id:2,icon:gf,title:"Android App Development",description:"Native Android applications with intuitive user interfaces and robust functionality",features:["Native Performance","Material Design","Offline Capabilities","Push Notifications","API Integration","Play Store Deployment"],technologies:["Kotlin","Android SDK","Firebase","Room DB","Retrofit"],price:"Starting at $3,500",popular:!0},{id:3,icon:xr,title:"AI Agent Creation",description:"Intelligent AI agents and chatbots that automate tasks and enhance user experiences",features:["Natural Language Processing","Custom Training","API Integration","Multi-platform Support","Analytics Dashboard","Continuous Learning"],technologies:["Python","TensorFlow","OpenAI API","LangChain","FastAPI"],price:"Starting at $5,000",popular:!1}];return S.jsx("section",{className:"py-20 bg-gray-50",children:S.jsxs("div",{className:"container-max section-padding",children:[S.jsxs(dt.div,{initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[S.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-black mb-6",children:"Services"}),S.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Comprehensive digital solutions tailored to bring your ideas to life"})]}),S.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:a.map((i,s)=>{const r=i.icon;return S.jsxs(dt.div,{initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:s*.2},viewport:{once:!0},className:"group relative",children:[i.popular&&S.jsx("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2 z-10",children:S.jsx("span",{className:"bg-black text-white px-4 py-2 rounded-full text-sm font-medium",children:"Most Popular"})}),S.jsx("div",{className:`h-full bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 transform group-hover:-translate-y-2 ${i.popular?"ring-2 ring-black":""}`,children:S.jsxs("div",{className:"p-8 h-full flex flex-col",children:[S.jsxs("div",{className:"text-center mb-6",children:[S.jsx("div",{className:"w-16 h-16 bg-black rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300",children:S.jsx(r,{size:32,className:"text-white"})}),S.jsx("h3",{className:"text-2xl font-bold text-black mb-3",children:i.title}),S.jsx("p",{className:"text-gray-600 leading-relaxed",children:i.description})]}),S.jsxs("div",{className:"flex-grow mb-6",children:[S.jsx("h4",{className:"font-semibold text-black mb-4",children:"What's Included:"}),S.jsx("ul",{className:"space-y-3",children:i.features.map((c,f)=>S.jsxs("li",{className:"flex items-center space-x-3",children:[S.jsx(OE,{size:16,className:"text-green-500 flex-shrink-0"}),S.jsx("span",{className:"text-gray-700 text-sm",children:c})]},f))})]}),S.jsxs("div",{className:"mb-6",children:[S.jsx("h4",{className:"font-semibold text-black mb-3",children:"Technologies:"}),S.jsx("div",{className:"flex flex-wrap gap-2",children:i.technologies.map((c,f)=>S.jsx("span",{className:"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium",children:c},f))})]}),S.jsxs("div",{className:"mt-auto",children:[S.jsxs("div",{className:"text-center mb-6",children:[S.jsx("p",{className:"text-2xl font-bold text-black mb-2",children:i.price}),S.jsx("p",{className:"text-gray-500 text-sm",children:"Custom quotes available"})]}),S.jsxs("button",{className:"w-full bg-black text-white py-3 rounded-lg font-medium hover:bg-gray-800 transition-all duration-300 flex items-center justify-center space-x-2 group",children:[S.jsx("span",{children:"Get Started"}),S.jsx(CE,{size:16,className:"group-hover:translate-x-1 transition-transform duration-300"})]})]})]})})]},i.id)})}),S.jsx(dt.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"text-center mt-16",children:S.jsxs("div",{className:"bg-black text-white rounded-2xl p-8 md:p-12",children:[S.jsx("h3",{className:"text-2xl md:text-3xl font-bold mb-4",children:"Have a Custom Project in Mind?"}),S.jsx("p",{className:"text-gray-300 text-lg mb-8 max-w-2xl mx-auto",children:"Every project is unique. Let's discuss your specific requirements and create a tailored solution that exceeds your expectations."}),S.jsx("button",{className:"bg-white text-black px-8 py-4 rounded-lg font-medium hover:bg-gray-200 transition-all duration-300 transform hover:scale-105",children:"Schedule a Consultation"})]})})]})})},T3=()=>S.jsx("div",{children:S.jsx(S3,{})}),A3=()=>{const[a,i]=C.useState({name:"",email:"",message:""}),[s,r]=C.useState(!1),[c,f]=C.useState(""),d=g=>{const{name:x,value:b}=g.target;i(M=>({...M,[x]:b}))},p=async g=>{g.preventDefault(),r(!0),setTimeout(()=>{r(!1),f("success"),i({name:"",email:"",message:""}),setTimeout(()=>f(""),3e3)},2e3)},m=[{icon:WE,label:"Email",value:"<EMAIL>",link:"mailto:<EMAIL>"},{icon:a3,label:"Phone",value:"+****************",link:"tel:+15551234567"},{icon:t3,label:"Location",value:"New York, NY",link:null}],h=[{icon:jc,label:"GitHub",url:"https://github.com/arkitkarmokar",color:"hover:text-gray-600"},{icon:JE,label:"LinkedIn",url:"https://linkedin.com/in/arkitkarmokar",color:"hover:text-blue-600"},{icon:h3,label:"Twitter",url:"https://twitter.com/arkitkarmokar",color:"hover:text-blue-400"}];return S.jsx("section",{className:"py-20 bg-black text-white min-h-screen flex items-center",children:S.jsxs("div",{className:"container-max section-padding w-full",children:[S.jsxs(dt.div,{initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[S.jsx("h2",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Let's Work Together"}),S.jsx("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto",children:"Have a project in mind? I'd love to hear about it. Send me a message and let's create something amazing together."})]}),S.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16",children:[S.jsx(dt.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:S.jsxs("div",{className:"bg-white text-black rounded-2xl p-8",children:[S.jsx("h3",{className:"text-2xl font-bold mb-6",children:"Send a Message"}),S.jsxs("form",{onSubmit:p,className:"space-y-6",children:[S.jsxs("div",{children:[S.jsx("label",{htmlFor:"name",className:"block text-sm font-medium mb-2",children:"Your Name"}),S.jsx("input",{type:"text",id:"name",name:"name",value:a.name,onChange:d,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300",placeholder:"John Doe"})]}),S.jsxs("div",{children:[S.jsx("label",{htmlFor:"email",className:"block text-sm font-medium mb-2",children:"Email Address"}),S.jsx("input",{type:"email",id:"email",name:"email",value:a.email,onChange:d,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300",placeholder:"<EMAIL>"})]}),S.jsxs("div",{children:[S.jsx("label",{htmlFor:"message",className:"block text-sm font-medium mb-2",children:"Message"}),S.jsx("textarea",{id:"message",name:"message",value:a.message,onChange:d,required:!0,rows:6,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 resize-none",placeholder:"Tell me about your project..."})]}),S.jsx("button",{type:"submit",disabled:s,className:"w-full bg-black text-white py-4 rounded-lg font-medium hover:bg-gray-800 transition-all duration-300 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",children:s?S.jsxs(S.Fragment,{children:[S.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),S.jsx("span",{children:"Sending..."})]}):S.jsxs(S.Fragment,{children:[S.jsx(l3,{size:20}),S.jsx("span",{children:"Send Message"})]})}),c==="success"&&S.jsx(dt.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-green-600 text-center font-medium",children:"Message sent successfully! I'll get back to you soon."})]})]})}),S.jsxs(dt.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"space-y-8",children:[S.jsxs("div",{children:[S.jsx("h3",{className:"text-2xl font-bold mb-6",children:"Get in Touch"}),S.jsx("div",{className:"space-y-4",children:m.map((g,x)=>{const b=g.icon;return S.jsxs(dt.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:x*.1},viewport:{once:!0},className:"flex items-center space-x-4",children:[S.jsx("div",{className:"w-12 h-12 bg-white bg-opacity-10 rounded-lg flex items-center justify-center",children:S.jsx(b,{size:20,className:"text-white"})}),S.jsxs("div",{children:[S.jsx("p",{className:"text-gray-400 text-sm",children:g.label}),g.link?S.jsx("a",{href:g.link,className:"text-white hover:text-gray-300 transition-colors duration-300",children:g.value}):S.jsx("p",{className:"text-white",children:g.value})]})]},x)})})]}),S.jsxs("div",{children:[S.jsx("h3",{className:"text-2xl font-bold mb-6",children:"Follow Me"}),S.jsx("div",{className:"flex space-x-4",children:h.map((g,x)=>{const b=g.icon;return S.jsx(dt.a,{href:g.url,target:"_blank",rel:"noopener noreferrer",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:x*.1},viewport:{once:!0},className:"w-12 h-12 bg-white bg-opacity-10 rounded-lg flex items-center justify-center hover:bg-opacity-20 transition-all duration-300 transform hover:scale-110",children:S.jsx(b,{size:20,className:"text-white"})},x)})})]}),S.jsxs(dt.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"bg-white bg-opacity-10 rounded-lg p-6",children:[S.jsx("h4",{className:"text-lg font-semibold mb-3",children:"Current Availability"}),S.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[S.jsx("div",{className:"w-3 h-3 bg-green-400 rounded-full"}),S.jsx("span",{className:"text-green-400 font-medium",children:"Available for new projects"})]}),S.jsx("p",{className:"text-gray-300 text-sm",children:"I'm currently accepting new projects and would love to work with you. Let's discuss your ideas!"})]})]})]})]})})},E3=()=>S.jsx("div",{children:S.jsx(A3,{})});function M3(){return S.jsx(Rb,{children:S.jsx(bE,{children:S.jsxs(ib,{children:[S.jsx(vl,{path:"/",element:S.jsx(v3,{})}),S.jsx(vl,{path:"/projects",element:S.jsx(b3,{})}),S.jsx(vl,{path:"/services",element:S.jsx(T3,{})}),S.jsx(vl,{path:"/contact",element:S.jsx(E3,{})})]})})})}dx.createRoot(document.getElementById("root")).render(S.jsx(C.StrictMode,{children:S.jsx(M3,{})}));
